#!/usr/bin/env python3
"""
Test script to verify the GUI integration of the configurable trailing space delay.
This script tests that the new GUI field is properly integrated.
"""

import sys
import os

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_integration():
    """Test that the GUI integration works correctly."""
    print("=== Testing GUI Integration ===\n")
    
    # Test that we can import the main module
    try:
        import main
        print("✅ Main module imported successfully")
    except Exception as e:
        print(f"❌ Failed to import main module: {e}")
        return False
    
    # Test that the ScreenTextTyper class has the new attributes
    try:
        # Create a mock root to avoid GUI dependencies
        class MockRoot:
            def title(self, title): pass
            def winfo_screenwidth(self): return 1920
            def winfo_screenheight(self): return 1080
            def quit(self): pass
        
        # Mock the GUI components
        class MockWidget:
            def __init__(self, *args, **kwargs): pass
            def pack(self, *args, **kwargs): pass
            def insert(self, pos, text): pass
            def get(self): return "1.0"  # Default value
            def delete(self, *args): pass
        
        # Patch the tkinter components
        original_button = main.tk.Button
        original_label = main.tk.Label
        original_entry = main.tk.Entry
        original_text = main.tk.Text
        
        main.tk.Button = MockWidget
        main.tk.Label = MockWidget
        main.tk.Entry = MockWidget
        main.tk.Text = MockWidget
        
        try:
            # Create the autotyper instance
            mock_root = MockRoot()
            app = main.ScreenTextTyper(mock_root)
            
            # Test that the new attributes exist
            assert hasattr(app, 'trailing_space_delay_label'), "Missing trailing_space_delay_label"
            assert hasattr(app, 'trailing_space_delay_entry'), "Missing trailing_space_delay_entry"
            assert hasattr(app, 'validate_trailing_space_delay'), "Missing validate_trailing_space_delay method"
            
            # Test that the default threshold is 1.0
            assert app.text_stability_threshold == 1.0, f"Expected default threshold 1.0, got {app.text_stability_threshold}"
            
            # Test the validation function
            assert app.validate_trailing_space_delay("0.5") == 0.5
            assert app.validate_trailing_space_delay("0.05") == 0.1  # Clamped to minimum
            assert app.validate_trailing_space_delay("15.0") == 10.0  # Clamped to maximum
            assert app.validate_trailing_space_delay("invalid") == 1.0  # Default on error
            
            print("✅ All GUI integration attributes present")
            print("✅ Default threshold correctly set to 1.0 seconds")
            print("✅ Validation function works correctly")
            
        finally:
            # Restore original tkinter components
            main.tk.Button = original_button
            main.tk.Label = original_label
            main.tk.Entry = original_entry
            main.tk.Text = original_text
            
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        return False
    
    print("\n=== GUI Integration Test Passed! ===")
    return True

def test_configuration_scenarios():
    """Test different configuration scenarios."""
    print("\n=== Testing Configuration Scenarios ===\n")
    
    scenarios = [
        ("Fast response", "0.3", 0.3),
        ("Default", "1.0", 1.0),
        ("Slow response", "3.5", 3.5),
        ("Very fast (clamped)", "0.05", 0.1),
        ("Very slow (clamped)", "20.0", 10.0),
        ("Invalid input", "abc", 1.0),
    ]
    
    for name, input_value, expected_output in scenarios:
        print(f"Scenario: {name}")
        print(f"  Input: '{input_value}'")
        print(f"  Expected: {expected_output}")
        
        # Test the validation
        import main
        
        class MockRoot:
            def title(self, title): pass
            def winfo_screenwidth(self): return 1920
            def winfo_screenheight(self): return 1080
            def quit(self): pass
        
        class MockWidget:
            def __init__(self, *args, **kwargs): pass
            def pack(self, *args, **kwargs): pass
            def insert(self, pos, text): pass
            def get(self): return input_value
            def delete(self, *args): pass
        
        # Patch tkinter temporarily
        original_components = {}
        for component in ['Button', 'Label', 'Entry', 'Text']:
            original_components[component] = getattr(main.tk, component)
            setattr(main.tk, component, MockWidget)
        
        try:
            app = main.ScreenTextTyper(MockRoot())
            result = app.validate_trailing_space_delay(input_value)
            assert result == expected_output, f"Expected {expected_output}, got {result}"
            print(f"  Result: {result} ✅")
        finally:
            # Restore components
            for component, original in original_components.items():
                setattr(main.tk, component, original)
        
        print()
    
    print("=== Configuration Scenarios Test Passed! ===")

if __name__ == "__main__":
    success = test_gui_integration()
    if success:
        test_configuration_scenarios()
        print("\n🎉 All tests passed! The configurable trailing space delay feature is working correctly.")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
