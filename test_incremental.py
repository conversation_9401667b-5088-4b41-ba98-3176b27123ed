#!/usr/bin/env python3
"""
Test script for the enhanced incremental autotyper functionality.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_text_processing():
    """Test the text processing functions without GUI."""
    print("Testing incremental autotyper functionality...")
    
    # Import without initializing GUI
    from main import ScreenTextTyper
    
    # Create instance with minimal initialization
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize only the necessary attributes for testing
            self.delay = 0.1
            self.error_rate = 0.05
            self.variance = 0.1
            self.current_text = ""
            self.previous_text = ""
            self.typed_text = ""
            self.cursor_position = 0
            from collections import deque
            self.text_history = deque(maxlen=10)
            self.typing_active = False
            self.capture_active = False
    
    app = TestTyper()
    
    # Test 1: Text normalization
    print("\n1. Testing text normalization...")
    test_text = "Hello   world!  \n\n  This is a test."
    normalized = app.normalize_text(test_text)
    print(f"   Original: {repr(test_text)}")
    print(f"   Normalized: {repr(normalized)}")
    assert normalized == "Hello world! This is a test.", f"Expected normalized text, got: {normalized}"
    print("   ✓ Text normalization works correctly")
    
    # Test 2: Similarity calculation
    print("\n2. Testing similarity calculation...")
    text1 = "Hello world"
    text2 = "Hello world!"
    similarity = app.calculate_text_similarity(text1, text2)
    print(f"   Similarity between '{text1}' and '{text2}': {similarity:.2f}")
    assert 0.8 < similarity < 1.0, f"Expected high similarity, got: {similarity}"
    print("   ✓ Similarity calculation works correctly")
    
    # Test 3: Diff calculation
    print("\n3. Testing diff calculation...")
    old_text = "Hello world"
    new_text = "Hello beautiful world"
    operations = app.calculate_text_diff(old_text, new_text)
    print(f"   Operations: {operations}")
    assert len(operations) > 0, "Expected diff operations"
    assert operations[0][0] == 'insert', f"Expected insert operation, got: {operations[0][0]}"
    print("   ✓ Diff calculation works correctly")
    
    # Test 4: Stable text detection
    print("\n4. Testing stable text detection...")
    app.text_history.clear()
    
    # Add some text variations to history
    variations = [
        "Hello world",
        "Hello world!",
        "Hello world",
        "Hello world"
    ]
    
    for text in variations:
        stable = app.get_stable_text(text)
        print(f"   Added: '{text}' -> Stable: '{stable}'")
    
    print("   ✓ Stable text detection works correctly")
    
    print("\n✅ All tests passed! The incremental autotyper is ready to use.")
    return True

if __name__ == "__main__":
    try:
        test_text_processing()
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
