#!/usr/bin/env python3
"""
Test script for the trailing space feature in the autotyper.
This script tests the logic by creating a minimal test class.
"""

import time

class MockAutotyper:
    """Mock autotyper class to test trailing space logic."""

    def __init__(self):
        # Enhanced state tracking for incremental updates
        self.current_text = ""
        self.previous_text = ""
        self.typed_text = ""
        self.cursor_position = 0
        self.typing_active = False
        self.capture_active = False

        # Trailing space feature state tracking
        self.last_typing_completion_time = 0
        self.text_stable_since = 0
        self.trailing_space_added = False
        self.text_stability_threshold = 2.0
        self.last_stable_text = ""

        # Mock typing actions
        self.typed_chars = []

    def check_and_add_trailing_space(self):
        """Check if conditions are met to add a trailing space and add it if needed."""
        current_time = time.time()

        # Check if we have finished typing all detected text
        if self.typed_text != self.current_text:
            # Still typing, reset trailing space state
            self.trailing_space_added = False
            return

        # Check if we already added a trailing space for this detection cycle
        if self.trailing_space_added:
            return

        # Check if enough time has passed since typing completion
        if current_time - self.last_typing_completion_time < self.text_stability_threshold:
            return

        # Check if text has remained stable (no new text detected)
        if self.current_text != self.last_stable_text:
            # Text changed, update stability tracking
            self.last_stable_text = self.current_text
            self.text_stable_since = current_time
            return

        # Check if text has been stable for the required duration
        if current_time - self.text_stable_since < self.text_stability_threshold:
            return

        # Check if the detected text doesn't already end with whitespace
        if not self.current_text or self.current_text[-1] in ' \t\n':
            return

        # All conditions met - add trailing space
        print("Adding trailing space after stable text detection")

        # Mock typing the space
        self.typed_chars.append(' ')

        # Update our tracking to include the space
        self.typed_text += ' '
        self.cursor_position += 1
        self.trailing_space_added = True

        print(f"Trailing space added. New typed text length: {len(self.typed_text)}")

def test_trailing_space_logic():
    """Test the trailing space logic without GUI dependencies."""

    # Create the mock autotyper instance
    typer = MockAutotyper()
    
    # Set up initial state
    typer.typing_active = True
    typer.capture_active = True
    typer.text_stability_threshold = 1.0  # Shorter threshold for testing
    
    print("=== Testing Trailing Space Feature ===\n")
    
    # Test Case 1: Basic trailing space addition
    print("Test Case 1: Basic trailing space addition")
    typer.current_text = "Hello world"
    typer.typed_text = "Hello world"
    typer.last_typing_completion_time = time.time() - 2.0  # 2 seconds ago
    typer.last_stable_text = "Hello world"
    typer.text_stable_since = time.time() - 2.0  # 2 seconds ago
    typer.trailing_space_added = False
    
    typer.check_and_add_trailing_space()
        
    print(f"  - Trailing space added: {typer.trailing_space_added}")
    print(f"  - Typed text: '{typer.typed_text}'")
    print(f"  - Expected: True and 'Hello world '")
    assert typer.trailing_space_added == True
    assert typer.typed_text == "Hello world "
    print("  ✓ PASSED\n")
    
    # Test Case 2: Don't add space if text already ends with whitespace
    print("Test Case 2: Don't add space if text already ends with whitespace")
    typer.current_text = "Hello world "
    typer.typed_text = "Hello world "
    typer.last_typing_completion_time = time.time() - 2.0
    typer.last_stable_text = "Hello world "
    typer.text_stable_since = time.time() - 2.0
    typer.trailing_space_added = False
    
    typer.check_and_add_trailing_space()
    
    print(f"  - Trailing space added: {typer.trailing_space_added}")
    print(f"  - Expected: False (text already ends with space)")
    assert typer.trailing_space_added == False
    print("  ✓ PASSED\n")
    
    # Test Case 3: Don't add space if still typing
    print("Test Case 3: Don't add space if still typing")
    typer.current_text = "Hello world test"
    typer.typed_text = "Hello world"  # Still typing
    typer.last_typing_completion_time = time.time() - 2.0
    typer.last_stable_text = "Hello world test"
    typer.text_stable_since = time.time() - 2.0
    typer.trailing_space_added = False
    
    typer.check_and_add_trailing_space()
    
    print(f"  - Trailing space added: {typer.trailing_space_added}")
    print(f"  - Expected: False (still typing)")
    assert typer.trailing_space_added == False
    print("  ✓ PASSED\n")
    
    # Test Case 4: Don't add space if not enough time has passed
    print("Test Case 4: Don't add space if not enough time has passed")
    typer.current_text = "Hello"
    typer.typed_text = "Hello"
    typer.last_typing_completion_time = time.time() - 0.5  # Only 0.5 seconds ago
    typer.last_stable_text = "Hello"
    typer.text_stable_since = time.time() - 0.5
    typer.trailing_space_added = False
    
    typer.check_and_add_trailing_space()
    
    print(f"  - Trailing space added: {typer.trailing_space_added}")
    print(f"  - Expected: False (not enough time passed)")
    assert typer.trailing_space_added == False
    print("  ✓ PASSED\n")
    
    # Test Case 5: Don't add space if already added for this cycle
    print("Test Case 5: Don't add space if already added for this cycle")
    typer.current_text = "Hello"
    typer.typed_text = "Hello"
    typer.last_typing_completion_time = time.time() - 2.0
    typer.last_stable_text = "Hello"
    typer.text_stable_since = time.time() - 2.0
    typer.trailing_space_added = True  # Already added
    
    original_typed_text = typer.typed_text
    typer.check_and_add_trailing_space()
    
    print(f"  - Typed text changed: {typer.typed_text != original_typed_text}")
    print(f"  - Expected: False (already added)")
    assert typer.typed_text == original_typed_text
    print("  ✓ PASSED\n")
    
    # Test Case 6: Text stability check
    print("Test Case 6: Text stability check")
    typer.current_text = "New text"
    typer.typed_text = "New text"
    typer.last_typing_completion_time = time.time() - 2.0
    typer.last_stable_text = "Old text"  # Different from current
    typer.trailing_space_added = False
    
    typer.check_and_add_trailing_space()
    
    print(f"  - Text stable since updated: {typer.last_stable_text == 'New text'}")
    print(f"  - Trailing space added: {typer.trailing_space_added}")
    print(f"  - Expected: Text updated to 'New text', no space added yet")
    assert typer.last_stable_text == "New text"
    assert typer.trailing_space_added == False
    print("  ✓ PASSED\n")
    
    print("=== All Tests Passed! ===")

if __name__ == "__main__":
    test_trailing_space_logic()
