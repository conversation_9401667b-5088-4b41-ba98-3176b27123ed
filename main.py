import tkinter as tk
from tkinter import Toplevel, Canvas, filedialog
import pytesseract
import threading
import pyautogui
import random
pyautogui.FAILSAFE = False
import re
from textblob import TextBlob
import time
from PIL import ImageGrab
import difflib
from collections import deque

# Try to import spell checking library, fall back to basic validation if not available
try:
    from spellchecker import Spell<PERSON>hecker
    SPELL_CHECKER_AVAILABLE = True
except ImportError:
    SPELL_CHECKER_AVAILABLE = False
    print("Warning: pyspellchecker not available. Install with: pip install pyspellchecker")

class ScreenTextTyper:
    def __init__(self, root):
        self.root = root
        self.root.title("Screen Text Typer")
        self.delay = 0.1
        self.error_rate = 0.05
        self.variance = 0.1

        # Initialize trailing space feature attributes BEFORE GUI setup
        # (needed because setup_gui() references text_stability_threshold)
        self.text_stability_threshold = 1.0  # Seconds to wait for text stability (default reduced to 1.0)

        self.setup_gui()
        self.lock = threading.Lock()  # Lock for pyautogui operations

        # Enhanced state tracking for incremental updates
        self.current_text = ""  # Current text in the selected area
        self.previous_text = ""  # Previous text state for comparison
        self.typed_text = ""  # Text that has been successfully typed
        self.cursor_position = 0  # Current cursor position in the text
        self.text_history = deque(maxlen=10)  # History of recent text states for stability
        self.typing_active = False
        self.capture_active = False

        # Remaining trailing space feature state tracking
        self.last_typing_completion_time = 0  # Timestamp of last typing completion
        self.text_stable_since = 0  # Timestamp when text became stable
        self.trailing_space_added = False  # Whether trailing space was added for current cycle
        self.last_stable_text = ""  # Last stable text for comparison

        # Duplicate line detection state tracking
        self.duplicate_lines_skipped = 0  # Count of duplicate lines skipped
        self.total_lines_processed = 0  # Total lines processed
        self.last_duplicate_detection_time = 0  # Timestamp of last duplicate detection

        # Initialize spell checker for dictionary validation
        self.spell_checker = None
        if SPELL_CHECKER_AVAILABLE:
            try:
                self.spell_checker = SpellChecker()
                print("Dictionary validation enabled with pyspellchecker")
            except Exception as e:
                print(f"Failed to initialize spell checker: {e}")
                self.spell_checker = None
        else:
            print("Dictionary validation using built-in word lists only")

    def setup_gui(self):
        self.select_area_button = tk.Button(self.root, text="Select Area", command=self.select_area)
        self.select_area_button.pack(pady=10)

        self.select_file_button = tk.Button(self.root, text="Select File", command=self.select_file)
        self.select_area_button.pack(pady=10)

        self.delay_label = tk.Label(self.root, text="Typing Delay (seconds):")
        self.delay_label.pack()
        self.delay_entry = tk.Entry(self.root)
        self.delay_entry.insert(0, str(self.delay))
        self.delay_entry.pack()

        self.variance_label = tk.Label(self.root, text="Delay Variance (%):")
        self.variance_label.pack()
        self.variance_entry = tk.Entry(self.root)
        self.variance_entry.insert(0, str(self.variance * 100))
        self.variance_entry.pack()

        self.error_rate_label = tk.Label(self.root, text="Error Rate (%):")
        self.error_rate_label.pack()
        self.error_rate_entry = tk.Entry(self.root)
        self.error_rate_entry.insert(0, str(self.error_rate * 100))
        self.error_rate_entry.pack()

        self.countdown_label = tk.Label(self.root, text="Countdown (seconds):")
        self.countdown_label.pack()
        self.countdown_entry = tk.Entry(self.root)
        self.countdown_entry.insert(0, "3")  # Default countdown of 3 seconds
        self.countdown_entry.pack()

        self.trailing_space_delay_label = tk.Label(self.root, text="Trailing Space Delay (seconds):")
        self.trailing_space_delay_label.pack()
        self.trailing_space_delay_entry = tk.Entry(self.root)
        self.trailing_space_delay_entry.insert(0, str(self.text_stability_threshold))
        self.trailing_space_delay_entry.pack()

        self.text_preview_label = tk.Label(self.root, text="Text Preview:")
        self.text_preview_label.pack()
        self.text_preview = tk.Text(self.root, height=10, width=50)
        self.text_preview.pack()

        self.save_text_button = tk.Button(self.root, text="Save Text", command=self.save_text)
        self.save_text_button.pack(pady=10)

        self.check_spelling_button = tk.Button(self.root, text="Check Spelling", command=self.check_spelling)                                                    
        self.check_spelling_button.pack(pady=10)

        self.start_button = tk.Button(self.root, text="Start Typing", command=self.start_typing)
        self.select_file_button.pack(pady=10)

        self.stop_button = tk.Button(self.root, text="Pause Typing", command=self.pause_typing)
        self.stop_button.pack(pady=10)
        self.start_button.pack(pady=10)

        self.status_button = tk.Button(self.root, text="Show Status", command=self.show_status)
        self.status_button.pack(pady=10)

        self.exit_button = tk.Button(self.root, text="Exit", command=self.exit_program)
        self.exit_button.pack(pady=10)

    def select_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            with open(file_path, 'r') as file:
                self.file_text = file.read()

    def select_area(self):
        self.selection_window = Toplevel(self.root)
        self.selection_window.geometry(f"{self.root.winfo_screenwidth()}x{self.root.winfo_screenheight()}+0+0")
        self.selection_window.attributes("-fullscreen", True)
        self.selection_window.wait_visibility()
        self.selection_window.wm_attributes("-alpha", 0.01)  # Almost fully transparent
        #self.selection_window.attributes("-topmost", True)
        self.selection_window.overrideredirect(True)
        self.selection_window.bind("<Escape>", lambda _: self.selection_window.destroy())

        self.canvas = Canvas(self.selection_window, cursor="cross")
        self.canvas.pack(fill="both", expand=True)
        self.canvas.bind("<ButtonPress-1>", self.on_button_press)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_button_release)

        self.start_x = self.start_y = 0
        self.rect = None

    def on_button_press(self, event):
        self.start_x = self.canvas.canvasx(event.x)
        self.start_y = self.canvas.canvasy(event.y)
        self.rect = self.canvas.create_rectangle(self.start_x, self.start_y, self.start_x, self.start_y, outline='red')

    def on_mouse_drag(self, event):
        cur_x, cur_y = (self.canvas.canvasx(event.x), self.canvas.canvasy(event.y))
        self.canvas.coords(self.rect, self.start_x, self.start_y, cur_x, cur_y)

    def on_button_release(self, event):
        end_x, end_y = (self.canvas.canvasx(event.x), self.canvas.canvasy(event.y))
        self.area = (int(self.start_x), int(self.start_y), int(end_x), int(end_y))
        self.selection_window.destroy()
        self.text_preview.delete(1.0, tk.END)
        self.text_preview.insert(tk.END, pytesseract.image_to_string(ImageGrab.grab(bbox=(self.start_x, self.start_y, end_x, end_y))))

    def save_text(self):
        self.file_text = self.text_preview.get(1.0, tk.END).strip()
        print("Saved Text Preview:")
        print(self.file_text)

    def check_spelling(self):
        text = self.text_preview.get(1.0, tk.END).strip()
        blob = TextBlob(text)
        misspelled_words = [word for word in blob.words if word != word.correct()]
        if misspelled_words:
            print("Misspelled Words:")
            for word in misspelled_words:
                print(f"{word} -> {word.correct()}")
        else:
            print("No spelling errors found.")

    def normalize_text(self, text):
        """Normalize text to handle OCR inconsistencies and text wrapping with dictionary validation."""
        if not text:
            return ""

        # First, handle line breaks intelligently
        normalized = self.handle_line_breaks_with_validation(text)

        # Remove extra whitespace (but preserve single spaces)
        normalized = re.sub(r'[ \t]+', ' ', normalized)

        # Remove common OCR artifacts
        normalized = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\]', '', normalized)

        # Final cleanup - strip leading/trailing whitespace
        return normalized.strip()

    def handle_line_breaks(self, text):
        """Intelligently handle line breaks to distinguish between wrapping and actual breaks."""
        if not text:
            return ""

        # Split text into lines, preserving original line content
        lines = text.split('\n')
        result = []

        for i, line in enumerate(lines):
            if i == 0:
                # First line - add as-is but strip trailing whitespace
                result.append(line.rstrip())
                continue

            if not line.strip():
                # Empty line - could be paragraph break
                if i < len(lines) - 1 and lines[i + 1].strip():
                    # Empty line followed by content - treat as paragraph break
                    result.append(' ')
                continue

            # Get the original previous line (before any processing)
            prev_line_original = lines[i - 1]
            prev_line_stripped = prev_line_original.rstrip()
            current_line_stripped = line.lstrip()

            if not prev_line_stripped:
                # Previous line was empty, start new content
                result.append(' ' + current_line_stripped)
                continue

            # Check if previous line ended with space (indicating intentional break)
            prev_had_trailing_space = len(prev_line_original) > len(prev_line_stripped)
            current_has_leading_space = len(line) > len(current_line_stripped)

            if prev_had_trailing_space or current_has_leading_space:
                # There was intentional spacing - preserve it
                result.append(' ' + current_line_stripped)
            elif self.is_word_wrapped(prev_line_stripped, current_line_stripped):
                # Word was wrapped - join without space
                result.append(current_line_stripped)
            else:
                # Normal line break - add space
                result.append(' ' + current_line_stripped)

        return ''.join(result)

    def handle_line_breaks_with_validation(self, text):
        """Handle line breaks with dictionary validation for improved accuracy."""
        if not text:
            return ""

        # Split text into lines, preserving original line content
        lines = text.split('\n')
        result = []

        for i, line in enumerate(lines):
            if i == 0:
                # First line - add as-is but strip trailing whitespace
                result.append(line.rstrip())
                continue

            if not line.strip():
                # Empty line - could be paragraph break
                if i < len(lines) - 1 and lines[i + 1].strip():
                    # Empty line followed by content - treat as paragraph break
                    result.append(' ')
                continue

            # Get the original previous line (before any processing)
            prev_line_original = lines[i - 1]
            prev_line_stripped = prev_line_original.rstrip()
            current_line_stripped = line.lstrip()

            if not prev_line_stripped:
                # Previous line was empty, start new content
                result.append(' ' + current_line_stripped)
                continue

            # Check if previous line ended with space (indicating intentional break)
            prev_had_trailing_space = len(prev_line_original) > len(prev_line_stripped)
            current_has_leading_space = len(line) > len(current_line_stripped)

            if prev_had_trailing_space or current_has_leading_space:
                # There was intentional spacing - preserve it
                result.append(' ' + current_line_stripped)
            else:
                # Use enhanced word boundary detection with dictionary validation
                should_join = self.is_word_wrapped_with_validation(prev_line_stripped, current_line_stripped)

                if should_join:
                    result.append(current_line_stripped)
                else:
                    result.append(' ' + current_line_stripped)

        return ''.join(result)

    def is_word_wrapped(self, prev_line, current_line):
        """Determine if a line break represents word wrapping using enhanced heuristics."""
        if not prev_line or not current_line:
            return False

        # Get the last word of previous line and first word of current line
        prev_words = prev_line.strip().split()
        current_words = current_line.strip().split()

        if not prev_words or not current_words:
            return False

        last_word = prev_words[-1]
        first_word = current_words[0]

        # Get the last character of previous line and first character of current line
        last_char = prev_line.rstrip()[-1] if prev_line.rstrip() else ''
        first_char = current_line.lstrip()[0] if current_line.lstrip() else ''

        # If previous line ends with space or current line starts with space, not wrapped
        if last_char.isspace() or first_char.isspace():
            return False

        # If previous line ends with punctuation, likely not wrapped
        if last_char in '.!?;:,':
            return False

        # If current line starts with punctuation, likely not wrapped
        if first_char in '.!?;:,':
            return False

        # If previous line ends with hyphen, likely hyphenated word
        if last_char == '-':
            return True

        # Enhanced semantic analysis
        return self.analyze_word_boundary(last_word, first_word)

    def is_word_wrapped_with_validation(self, prev_line, current_line):
        """Enhanced word wrapping detection with dictionary validation."""
        if not prev_line or not current_line:
            return False

        # Get the last word of previous line and first word of current line
        prev_words = prev_line.strip().split()
        current_words = current_line.strip().split()

        if not prev_words or not current_words:
            return False

        last_word = prev_words[-1]
        first_word = current_words[0]

        # Get the last character of previous line and first character of current line
        last_char = prev_line.rstrip()[-1] if prev_line.rstrip() else ''
        first_char = current_line.lstrip()[0] if current_line.lstrip() else ''

        # Basic checks first
        if last_char.isspace() or first_char.isspace():
            return False
        if last_char in '.!?;:,':
            return False
        if first_char in '.!?;:,':
            return False
        if last_char == '-':
            return True

        # Get initial decision from semantic analysis
        semantic_decision = self.analyze_word_boundary(last_word, first_word)

        # Apply dictionary validation to improve accuracy
        return self.validate_word_boundary_decision(last_word, first_word, semantic_decision)

    def analyze_word_boundary(self, last_word, first_word):
        """Analyze whether two words should be joined or separated using semantic heuristics."""

        # Check for obvious word continuation patterns first (highest priority)
        if self.is_likely_word_continuation(last_word, first_word):
            return True

        # Check for common compound word patterns (high priority)
        if self.is_likely_compound_word(last_word, first_word):
            return True

        # Check word length and breaking patterns
        if self.suggests_line_wrap(last_word, first_word):
            return True

        # Check for capitalization patterns that suggest separate words
        if self.suggests_separate_words(last_word, first_word):
            return False

        # Default to separate words (safer approach)
        return False

    def is_likely_word_continuation(self, last_word, first_word):
        """Check if the second word is likely a continuation of the first."""

        # Common suffix patterns that suggest word continuation
        common_suffixes = {
            'ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion', 'ness', 'ment',
            'able', 'ible', 'ful', 'less', 'ous', 'ive', 'al', 'ic', 'ical'
        }

        # If the first word looks incomplete and second word is a common suffix
        if (len(last_word) <= 3 and
            first_word.lower() in common_suffixes and
            not last_word.endswith(('ing', 'ed', 'er', 'ly'))):
            return True

        # Check for common prefixes that suggest word continuation
        if (len(last_word) <= 3 and
            last_word.lower() in ('un', 're', 'pre', 'dis', 'mis', 'in', 'ex', 'de')):
            return True

        # Check if joining creates a recognizable word pattern
        combined = last_word.lower() + first_word.lower()
        if self.is_recognizable_word_pattern(combined):
            return True

        return False

    def suggests_separate_words(self, last_word, first_word):
        """Check if capitalization or other patterns suggest separate words."""

        # If the second word starts with a capital letter and the first doesn't end with hyphen
        # it's likely a new word/sentence
        if (first_word[0].isupper() and
            len(last_word) > 1 and
            not last_word.endswith('-') and
            not last_word.isupper()):  # Unless it's an acronym
            return True

        # If both words are capitalized, likely proper nouns or separate words
        if (last_word[0].isupper() and first_word[0].isupper() and
            len(last_word) > 2 and len(first_word) > 2):
            return True

        # If the last word is a complete common word AND not part of a compound
        if (self.is_complete_common_word(last_word) and
            not self.is_likely_compound_word(last_word, first_word)):
            return True

        return False

    def is_likely_compound_word(self, last_word, first_word):
        """Check if the words form a likely compound word."""

        # Common compound word patterns
        compound_patterns = {
            # First part of compound words
            'some': ['thing', 'where', 'how', 'one', 'body', 'time'],
            'any': ['thing', 'where', 'how', 'one', 'body', 'time'],
            'every': ['thing', 'where', 'how', 'one', 'body', 'time'],
            'no': ['thing', 'where', 'how', 'one', 'body'],
            'under': ['stand', 'ground', 'water', 'line', 'way'],
            'over': ['come', 'look', 'head', 'time', 'flow'],
            'out': ['side', 'put', 'come', 'look', 'line', 'door', 'going'],
            'up': ['date', 'grade', 'load', 'set', 'ward', 'stairs', 'hill'],
            'down': ['load', 'ward', 'hill', 'town', 'fall', 'stairs'],
            'good': ['bye', 'will', 'ness'],  # Note: "morning" deliberately excluded
            'after': ['noon', 'ward', 'math'],
            'with': ['out', 'in'],
        }

        last_lower = last_word.lower()
        first_lower = first_word.lower()

        if last_lower in compound_patterns:
            if first_lower in compound_patterns[last_lower]:
                return True

        return False

    def suggests_line_wrap(self, last_word, first_word):
        """Check if word length and patterns suggest line wrapping."""

        # If the last word is very short (1-2 chars) and looks like a prefix
        if (len(last_word) <= 2 and
            last_word.lower() in ['a', 'an', 'be', 'de', 're', 'un', 'in', 'ex']):
            return True

        # If the first word is very short and looks like a suffix
        if (len(first_word) <= 3 and
            first_word.lower() in ['ing', 'ed', 'er', 'ly', 'al', 'ic']):
            return True

        return False

    def is_recognizable_word_pattern(self, combined_word):
        """Check if the combined word follows recognizable English patterns."""

        # Common word patterns and endings
        if len(combined_word) < 4:
            return False

        # Check for common word endings
        common_endings = [
            'tion', 'sion', 'ness', 'ment', 'able', 'ible', 'ful', 'less',
            'ous', 'ive', 'ing', 'ed', 'er', 'est', 'ly', 'al', 'ic', 'ical'
        ]

        for ending in common_endings:
            if combined_word.endswith(ending) and len(combined_word) > len(ending) + 2:
                return True

        return False

    def is_complete_common_word(self, word):
        """Check if a word is a complete common English word."""

        # List of very common complete words that are unlikely to be partial
        common_complete_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy',
            'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'word', 'work',
            'first', 'would', 'there', 'could', 'water', 'after', 'back', 'other',
            'many', 'them', 'these', 'come', 'made', 'most', 'over', 'said', 'some',
            'time', 'very', 'when', 'much', 'before', 'here', 'through', 'just',
            'where', 'much', 'good', 'sentence', 'right', 'think', 'great', 'help',
            'hello', 'world', 'people', 'place', 'year', 'years', 'state', 'show',
            'every', 'good', 'those', 'feel', 'life', 'fact', 'hand', 'high',
            'number', 'part', 'point', 'tell', 'want', 'because', 'same', 'turn',
            'three', 'small', 'large', 'next', 'early', 'young', 'important', 'few',
            'public', 'bad', 'same', 'able'
        }

        return word.lower() in common_complete_words

    def validate_word_boundary_decision(self, last_word, first_word, semantic_decision):
        """Validate word boundary decision using dictionary lookup and confidence scoring."""

        # Calculate confidence score for the semantic decision
        confidence = self.calculate_decision_confidence(last_word, first_word, semantic_decision)

        # If confidence is high, trust the semantic decision
        if confidence >= 0.8:
            return semantic_decision

        # For low confidence decisions, use dictionary validation
        if semantic_decision:
            # Semantic analysis suggests joining - validate the result
            return self.validate_word_joining(last_word, first_word)
        else:
            # Semantic analysis suggests separating - validate the fragments
            return self.validate_word_separation(last_word, first_word)

    def calculate_decision_confidence(self, last_word, first_word, decision):
        """Calculate confidence score for a word boundary decision."""
        confidence = 0.5  # Base confidence

        # High confidence indicators for joining
        if decision:
            # Hyphenated words
            if last_word.endswith('-'):
                confidence += 0.4

            # Common suffixes
            if first_word.lower() in {'ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion'}:
                confidence += 0.3

            # Common prefixes
            if last_word.lower() in {'un', 're', 'pre', 'dis', 'mis', 'in', 'ex'}:
                confidence += 0.3

            # Known compound patterns
            if self.is_likely_compound_word(last_word, first_word):
                confidence += 0.2

        # High confidence indicators for separating
        else:
            # Capitalization suggests new word
            if first_word[0].isupper() and not last_word.endswith('-'):
                confidence += 0.3

            # Both words are complete common words
            if (self.is_complete_common_word(last_word) and
                self.is_complete_common_word(first_word)):
                confidence += 0.2

        return min(confidence, 1.0)

    def validate_word_joining(self, last_word, first_word):
        """Validate whether joining two words creates a valid word."""
        combined_word = last_word + first_word

        # Check if the combined word is valid
        if self.is_valid_word(combined_word):
            return True

        # Check if either part alone is invalid (suggesting they should be joined)
        last_word_valid = self.is_valid_word(last_word)
        first_word_valid = self.is_valid_word(first_word)

        # If both parts are invalid separately, likely they should be joined
        if not last_word_valid and not first_word_valid:
            return True

        # If one part is invalid and the other is very short, likely joining
        if (not last_word_valid and len(first_word) <= 3) or (not first_word_valid and len(last_word) <= 3):
            return True

        # Default to not joining if combined word is invalid
        return False

    def validate_word_separation(self, last_word, first_word):
        """Validate whether separating words is correct."""
        combined_word = last_word + first_word

        # If the combined word would be invalid, separation is correct
        if not self.is_valid_word(combined_word):
            return False

        # If both parts are valid separately, separation is likely correct
        if self.is_valid_word(last_word) and self.is_valid_word(first_word):
            return False

        # If combined word is valid but parts aren't, should probably join
        return True

    def is_valid_word(self, word):
        """Check if a word is valid using available dictionary resources."""
        if not word or len(word) < 2:
            return False

        word_lower = word.lower()

        # Check against built-in common words first
        if self.is_complete_common_word(word):
            return True

        # Check against extended built-in dictionary
        if self.is_in_extended_dictionary(word_lower):
            return True

        # Use spell checker if available
        if self.spell_checker:
            try:
                return word_lower in self.spell_checker
            except Exception:
                pass

        # Fallback: check if it follows common English patterns
        return self.follows_english_patterns(word_lower)

    def is_in_extended_dictionary(self, word):
        """Check against an extended built-in dictionary of common English words."""
        # Extended dictionary of common English words
        extended_words = {
            # Common verbs
            'walk', 'walking', 'walked', 'run', 'running', 'ran', 'jump', 'jumping', 'jumped',
            'think', 'thinking', 'thought', 'speak', 'speaking', 'spoke', 'write', 'writing', 'wrote',
            'read', 'reading', 'look', 'looking', 'looked', 'find', 'finding', 'found',

            # Common nouns
            'house', 'car', 'book', 'table', 'chair', 'computer', 'phone', 'door', 'window',
            'tree', 'flower', 'animal', 'person', 'child', 'children', 'family', 'friend',
            'school', 'teacher', 'student', 'class', 'lesson', 'homework', 'test', 'exam',

            # Common adjectives
            'good', 'bad', 'big', 'small', 'large', 'little', 'old', 'new', 'young', 'beautiful',
            'ugly', 'happy', 'sad', 'angry', 'excited', 'tired', 'hungry', 'thirsty',

            # Common compound words
            'something', 'anything', 'everything', 'nothing', 'someone', 'anyone', 'everyone',
            'somewhere', 'anywhere', 'everywhere', 'nowhere', 'understand', 'overcome',
            'outside', 'inside', 'upstairs', 'downstairs', 'update', 'download', 'upload',
            'background', 'foreground', 'playground', 'classroom', 'bedroom', 'bathroom',

            # Technical terms
            'computer', 'software', 'hardware', 'internet', 'website', 'email', 'password',
            'database', 'program', 'application', 'system', 'network', 'server', 'client',

            # Common words that might be split
            'through', 'finally', 'really', 'actually', 'probably', 'definitely', 'certainly',
            'beautiful', 'wonderful', 'terrible', 'horrible', 'amazing', 'incredible',
            'important', 'different', 'difficult', 'possible', 'impossible', 'available',
            'necessary', 'interesting', 'comfortable', 'uncomfortable', 'responsible',
        }

        return word in extended_words

    def follows_english_patterns(self, word):
        """Check if a word follows common English patterns."""
        if len(word) < 3:
            return False

        # Common English word patterns
        common_patterns = [
            r'^[a-z]+ing$',      # -ing words
            r'^[a-z]+ed$',       # -ed words
            r'^[a-z]+er$',       # -er words
            r'^[a-z]+est$',      # -est words
            r'^[a-z]+ly$',       # -ly words
            r'^[a-z]+tion$',     # -tion words
            r'^[a-z]+sion$',     # -sion words
            r'^[a-z]+ness$',     # -ness words
            r'^[a-z]+ment$',     # -ment words
            r'^[a-z]+able$',     # -able words
            r'^[a-z]+ible$',     # -ible words
            r'^[a-z]+ful$',      # -ful words
            r'^[a-z]+less$',     # -less words
            r'^un[a-z]+$',       # un- words
            r'^re[a-z]+$',       # re- words
            r'^pre[a-z]+$',      # pre- words
        ]

        for pattern in common_patterns:
            if re.match(pattern, word):
                return True

        return False

    def calculate_text_similarity(self, text1, text2):
        """Calculate similarity between two text strings."""
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0

        # Use sequence matcher to get similarity ratio
        matcher = difflib.SequenceMatcher(None, text1, text2)
        return matcher.ratio()

    def get_stable_text(self, new_text):
        """Get stable text by comparing with recent history."""
        normalized_text = self.normalize_text(new_text)

        # Add to history
        self.text_history.append(normalized_text)

        # If we don't have enough history, return the normalized text
        if len(self.text_history) < 3:
            return normalized_text

        # Find the most consistent text in recent history
        recent_texts = list(self.text_history)[-3:]
        best_text = normalized_text
        best_score = 0

        for candidate in recent_texts:
            total_similarity = sum(self.calculate_text_similarity(candidate, other)
                                 for other in recent_texts)
            if total_similarity > best_score:
                best_score = total_similarity
                best_text = candidate

        return best_text

    def calculate_append_suffix(self, typed_text, current_text):
        """Calculate the suffix that needs to be appended (append-only logic)."""
        if not current_text:
            return ""

        if not typed_text:
            return current_text

        # Check if current text starts with typed text
        if current_text.startswith(typed_text):
            # Return the new suffix that needs to be typed
            return current_text[len(typed_text):]
        else:
            # Text was reset or changed - return entire current text
            return current_text

    def detect_duplicate_lines(self, typed_text, current_text):
        """Detect complete duplicate lines between typed text and current text.

        Returns:
            tuple: (duplicate_lines_count, resume_position, skipped_content)
        """
        if not typed_text or not current_text:
            return 0, 0, ""

        # Split both texts into lines
        typed_lines = typed_text.split('\n')
        current_lines = current_text.split('\n')

        duplicate_count = 0
        resume_position = 0
        skipped_content = ""

        # Compare lines from the beginning
        min_lines = min(len(typed_lines), len(current_lines))

        for i in range(min_lines):
            typed_line = typed_lines[i].strip()
            current_line = current_lines[i].strip()

            # Check if lines are identical and contain multiple words (not single words)
            if (typed_line == current_line and
                len(typed_line.split()) > 1 and  # Must have multiple words
                len(typed_line) > 0):  # Must not be empty

                duplicate_count += 1
                # Calculate position after this line
                # Sum up lengths of all lines up to and including current line, plus newlines
                resume_position = 0
                for j in range(i + 1):
                    resume_position += len(current_lines[j])
                    if j < i:  # Add newline after each line except the current one
                        resume_position += 1

                # If there are more lines after this duplicate, add newline after current line
                if i < len(current_lines) - 1:
                    resume_position += 1

                # Add to skipped content
                if skipped_content:
                    skipped_content += '\n'
                skipped_content += current_line

            else:
                # Lines differ, stop checking
                break

        return duplicate_count, resume_position, skipped_content

    def calculate_smart_append_suffix(self, typed_text, current_text):
        """Calculate suffix with intelligent duplicate line detection and skipping."""
        if not current_text:
            return "", 0, ""

        if not typed_text:
            return current_text, 0, ""

        # First check if current text starts with typed text (basic prefix match)
        if not current_text.startswith(typed_text):
            # Text was reset or changed - return entire current text
            return current_text, 0, ""

        # Detect duplicate lines
        duplicate_count, resume_position, skipped_content = self.detect_duplicate_lines(typed_text, current_text)

        if duplicate_count > 0:
            # We found duplicate lines - calculate suffix from resume position
            suffix = current_text[resume_position:] if resume_position < len(current_text) else ""

            print(f"Duplicate line detection: {duplicate_count} lines skipped")
            print(f"Skipped content: '{skipped_content[:50]}...'")
            print(f"Resume position: {resume_position}")
            print(f"Suffix to type: '{suffix[:50]}...'")

            # Update statistics
            self.duplicate_lines_skipped += duplicate_count
            self.total_lines_processed += duplicate_count
            self.last_duplicate_detection_time = time.time()

            return suffix, resume_position, skipped_content
        else:
            # No duplicates found - use standard append logic
            suffix = current_text[len(typed_text):]
            return suffix, len(typed_text), ""

    def random_delay(self):
        """Generate a random delay based on the configured delay and variance."""
        return self.delay + random.uniform(-self.variance, self.variance) * self.delay

    def validate_trailing_space_delay(self, value):
        """Validate and clamp the trailing space delay value to acceptable range."""
        try:
            delay = float(value)
            # Clamp to range [0.1, 10.0] seconds
            if delay < 0.1:
                print(f"Warning: Trailing space delay {delay} too small, setting to minimum 0.1 seconds")
                return 0.1
            elif delay > 10.0:
                print(f"Warning: Trailing space delay {delay} too large, setting to maximum 10.0 seconds")
                return 10.0
            else:
                return delay
        except ValueError:
            print(f"Error: Invalid trailing space delay value '{value}', using default 1.0 seconds")
            return 1.0

    def check_and_add_trailing_space(self):
        """Check if conditions are met to add a trailing space and add it if needed."""
        current_time = time.time()

        # Check if we have finished typing all detected text
        if self.typed_text != self.current_text:
            # Still typing, reset trailing space state
            self.trailing_space_added = False
            return

        # Check if we already added a trailing space for this detection cycle
        if self.trailing_space_added:
            return

        # Check if enough time has passed since typing completion
        if current_time - self.last_typing_completion_time < self.text_stability_threshold:
            return

        # Check if text has remained stable (no new text detected)
        if self.current_text != self.last_stable_text:
            # Text changed, update stability tracking
            self.last_stable_text = self.current_text
            self.text_stable_since = current_time
            return

        # Check if text has been stable for the required duration
        if current_time - self.text_stable_since < self.text_stability_threshold:
            return

        # Check if the detected text doesn't already end with whitespace
        if not self.current_text or self.current_text[-1] in ' \t\n':
            return

        # All conditions met - add trailing space
        print("Adding trailing space after stable text detection")

        # Position cursor at the end and add space
        self.position_cursor_at_end()

        with self.lock:
            pyautogui.typewrite(' ', interval=self.random_delay())

        # Update our tracking to include the space
        self.typed_text += ' '
        self.cursor_position += 1
        self.trailing_space_added = True

        print(f"Trailing space added. New typed text length: {len(self.typed_text)}")

    def start_typing(self):
        """Start the enhanced incremental typing process."""
        self.delay = float(self.delay_entry.get())
        self.variance = float(self.variance_entry.get()) / 100
        self.error_rate = float(self.error_rate_entry.get()) / 100

        # Read and validate trailing space delay from GUI
        self.text_stability_threshold = self.validate_trailing_space_delay(
            self.trailing_space_delay_entry.get()
        )

        if not hasattr(self, 'area'):
            print("Please select an area first!")
            return

        # Reset state for new typing session
        self.current_text = ""
        self.previous_text = ""
        self.typed_text = ""
        self.cursor_position = 0
        self.text_history.clear()

        # Reset trailing space state
        self.last_typing_completion_time = 0
        self.text_stable_since = 0
        self.trailing_space_added = False
        self.last_stable_text = ""

        # Reset duplicate detection state
        self.duplicate_lines_skipped = 0
        self.total_lines_processed = 0
        self.last_duplicate_detection_time = 0

        # Countdown
        countdown = int(self.countdown_entry.get())
        for i in range(countdown, 0, -1):
            print(f"Starting in {i}...")
            time.sleep(1)

        # Start the typing process
        self.typing_active = True
        self.capture_active = True

        x1, y1, x2, y2 = self.area

        # Start capture and typing threads
        capture_thread = threading.Thread(target=self.enhanced_capture_text, args=((x1, y1, x2, y2),))
        typing_thread = threading.Thread(target=self.enhanced_typing_loop)

        capture_thread.daemon = True
        typing_thread.daemon = True

        capture_thread.start()
        typing_thread.start()

        print("Enhanced incremental typing started!")

    def pause_typing(self):
        """Pause the typing process."""
        self.typing_active = False
        self.capture_active = False
        print("Typing paused.")

    def enhanced_capture_text(self, bbox):
        """Enhanced text capture with stability checking."""
        while self.capture_active:
            try:
                # Capture image and extract text
                img = ImageGrab.grab(bbox=bbox)
                raw_text = pytesseract.image_to_string(img).strip()

                # Get stable text using history
                stable_text = self.get_stable_text(raw_text)

                # Update current text if it's significantly different
                similarity = self.calculate_text_similarity(self.current_text, stable_text)

                if similarity < 0.9:  # Text has changed significantly
                    self.previous_text = self.current_text
                    self.current_text = stable_text

                    # Update text preview
                    self.text_preview.delete(1.0, tk.END)
                    self.text_preview.insert(tk.END, stable_text)

                    print(f"Text updated: '{stable_text[:50]}...' (similarity: {similarity:.2f})")

                time.sleep(0.5)  # Capture every 500ms for responsiveness

            except Exception as e:
                print(f"Error in text capture: {e}")
                time.sleep(1)

    def enhanced_typing_loop(self):
        """Enhanced typing loop with incremental updates and trailing space feature."""
        while self.typing_active:
            try:
                # Check if text has changed
                if self.current_text != self.previous_text:
                    self.process_text_changes()

                # Check if we should add a trailing space
                self.check_and_add_trailing_space()

                time.sleep(0.1)  # Check for changes frequently

            except Exception as e:
                print(f"Error in typing loop: {e}")
                time.sleep(1)

    def process_text_changes(self):
        """Process changes using append-only logic with intelligent duplicate line detection."""
        if not self.current_text:
            return

        # Check if current text starts with our typed text (prefix match)
        if not self.current_text.startswith(self.typed_text):
            # Text area was cleared or changed - reset and start fresh
            print(f"Text reset detected. Starting fresh with: '{self.current_text[:50]}...'")
            self.typed_text = ""
            self.cursor_position = 0

            # Position cursor at the beginning and type all current text
            self.position_cursor_at_end()
            if self.current_text:
                self.type_text_with_effects(self.current_text)
                self.typed_text = self.current_text
                self.cursor_position = len(self.current_text)

                # Mark typing completion time for trailing space feature
                self.last_typing_completion_time = time.time()
                self.trailing_space_added = False  # Reset for new text
        else:
            # Current text starts with typed text - use smart duplicate detection
            new_text, resume_position, skipped_content = self.calculate_smart_append_suffix(self.typed_text, self.current_text)

            if new_text:
                if skipped_content:
                    # We skipped duplicate lines - update typed_text to include skipped content
                    # and position cursor at resume point
                    print(f"Skipping duplicate lines and appending new text: '{new_text[:50]}...'")

                    # Update typed_text to include content up to resume position
                    self.typed_text = self.current_text[:resume_position]
                    self.cursor_position = resume_position

                    # Position cursor at resume point
                    self.position_cursor_at(resume_position)
                else:
                    print(f"Appending new text: '{new_text[:50]}...'")

                    # Ensure cursor is at the end of previously typed text
                    self.position_cursor_at_end()

                # Type only the new text
                self.type_text_with_effects(new_text)

                # Update our tracking
                self.typed_text = self.current_text
                self.cursor_position = len(self.current_text)

                # Mark typing completion time for trailing space feature
                self.last_typing_completion_time = time.time()
                self.trailing_space_added = False  # Reset for new text

        # Update previous text for next comparison
        self.previous_text = self.current_text

    def position_cursor_at_end(self):
        """Position cursor at the end of the currently typed text."""
        target_position = len(self.typed_text)

        if target_position == self.cursor_position:
            return

        print(f"Positioning cursor at end (position {target_position})")

        # Calculate movement needed
        movement = target_position - self.cursor_position

        with self.lock:
            if movement > 0:
                # Move cursor forward to end
                for _ in range(movement):
                    pyautogui.press('right')
                    time.sleep(0.01)
            else:
                # Move cursor backward to end position
                for _ in range(abs(movement)):
                    pyautogui.press('left')
                    time.sleep(0.01)

        self.cursor_position = target_position

    def position_cursor_at(self, target_position):
        """Position cursor at the specified text position."""
        if target_position == self.cursor_position:
            return

        # Calculate movement needed
        movement = target_position - self.cursor_position

        with self.lock:
            if movement > 0:
                # Move cursor forward
                for _ in range(movement):
                    pyautogui.press('right')
                    time.sleep(0.01)
            else:
                # Move cursor backward
                for _ in range(abs(movement)):
                    pyautogui.press('left')
                    time.sleep(0.01)

        self.cursor_position = target_position

    def type_text_with_effects(self, text):
        """Type text with realistic effects (delays, errors, corrections)."""
        for char in text:
            if not self.typing_active:
                break

            # Handle newlines
            if char == '\n':
                with self.lock:
                    pyautogui.press('enter')
                    time.sleep(self.random_delay())
                continue

            # Simulate typing errors
            if random.random() < self.error_rate:
                # Type a wrong character first
                typo = random.choice('abcdefghijklmnopqrstuvwxyz')
                with self.lock:
                    pyautogui.typewrite(typo, interval=self.random_delay())
                    time.sleep(0.1)  # Brief pause before correction
                    pyautogui.press('backspace')
                    time.sleep(0.05)
                    pyautogui.typewrite(char, interval=self.random_delay())
            else:
                # Type the correct character
                with self.lock:
                    pyautogui.typewrite(char, interval=self.random_delay())

    def type_text(self, text):
        """Legacy method - redirects to new implementation."""
        self.type_text_with_effects(text)

    def show_status(self):
        """Display current status and statistics."""
        # Calculate what would be appended next
        next_suffix = self.calculate_append_suffix(self.typed_text, self.current_text)
        is_prefix_match = self.current_text.startswith(self.typed_text) if self.typed_text else True

        # Calculate trailing space status
        current_time = time.time()
        time_since_completion = current_time - self.last_typing_completion_time if self.last_typing_completion_time > 0 else 0
        time_since_stable = current_time - self.text_stable_since if self.text_stable_since > 0 else 0
        text_ends_with_whitespace = self.current_text and self.current_text[-1] in ' \t\n'

        status_info = f"""
=== Append-Only Autotyper Status ===
Mode: APPEND-ONLY (No deletions/replacements)
Typing Active: {self.typing_active}
Capture Active: {self.capture_active}

Text Analysis:
- Current Text Length: {len(self.current_text)}
- Typed Text Length: {len(self.typed_text)}
- Cursor Position: {self.cursor_position}
- Text History Size: {len(self.text_history)}
- Prefix Match: {is_prefix_match}
- Next Suffix Length: {len(next_suffix)}

Trailing Space Feature:
- Trailing Space Added: {self.trailing_space_added}
- Time Since Typing Completion: {time_since_completion:.1f}s
- Time Since Text Stable: {time_since_stable:.1f}s
- Text Ends With Whitespace: {text_ends_with_whitespace}
- Stability Threshold: {self.text_stability_threshold}s (user-configurable: 0.1-10.0s)

Duplicate Line Detection:
- Duplicate Lines Skipped: {self.duplicate_lines_skipped}
- Total Lines Processed: {self.total_lines_processed}
- Efficiency: {(self.duplicate_lines_skipped / max(self.total_lines_processed, 1) * 100):.1f}% lines skipped
- Last Detection: {time.time() - self.last_duplicate_detection_time:.1f}s ago (if detected)

Text Previews:
- Current Text: '{self.current_text[:100]}...'
- Typed Text: '{self.typed_text[:100]}...'
- Next Suffix: '{next_suffix[:50]}...'

Settings:
- Delay: {self.delay}s
- Variance: {self.variance * 100}%
- Error Rate: {self.error_rate * 100}%
"""
        print(status_info)

    def exit_program(self):
        """Stop typing and close the application."""
        self.typing_active = False
        self.capture_active = False
        print("Shutting down autotyper...")
        self.root.quit()

if __name__ == "__main__":
    root = tk.Tk()
    app = ScreenTextTyper(root)
    root.mainloop()
