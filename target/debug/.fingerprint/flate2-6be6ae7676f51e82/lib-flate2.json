{"rustc": 9335450818755339689, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2241668132362809309, "path": 2755672723501474925, "deps": [[4675849561795547236, "miniz_oxide", false, 3395117108643758446], [5466618496199522463, "crc32fast", false, 7014629220784685464]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-6be6ae7676f51e82/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}