{"rustc": 9335450818755339689, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 488247897591652195, "deps": [[1615478164327904835, "pin_utils", false, 13820887002284949096], [1906322745568073236, "pin_project_lite", false, 13202821281350094597], [6955678925937229351, "slab", false, 3529683633028822281], [7620660491849607393, "futures_core", false, 3496522457058825908], [10565019901765856648, "futures_macro", false, 12783876376699465939], [16240732885093539806, "futures_task", false, 16906901257812956599]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-7d9ad4946a736f0d/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}