#!/usr/bin/env python3
"""
Test script for the intelligent duplicate line detection feature.
This script tests the new duplicate detection and skipping functionality.
"""

import time

class MockAutotyper:
    """Mock autotyper class to test duplicate line detection logic."""
    
    def __init__(self):
        # State tracking
        self.typed_text = ""
        self.current_text = ""
        self.cursor_position = 0
        
        # Duplicate detection state
        self.duplicate_lines_skipped = 0
        self.total_lines_processed = 0
        self.last_duplicate_detection_time = 0
    
    def detect_duplicate_lines(self, typed_text, current_text):
        """Detect complete duplicate lines between typed text and current text."""
        if not typed_text or not current_text:
            return 0, 0, ""
        
        # Split both texts into lines
        typed_lines = typed_text.split('\n')
        current_lines = current_text.split('\n')
        
        duplicate_count = 0
        resume_position = 0
        skipped_content = ""
        
        # Compare lines from the beginning
        min_lines = min(len(typed_lines), len(current_lines))
        
        for i in range(min_lines):
            typed_line = typed_lines[i].strip()
            current_line = current_lines[i].strip()
            
            # Check if lines are identical and contain multiple words (not single words)
            if (typed_line == current_line and 
                len(typed_line.split()) > 1 and  # Must have multiple words
                len(typed_line) > 0):  # Must not be empty
                
                duplicate_count += 1
                # Calculate position after this line (including newline if not last line)
                line_end_pos = len('\n'.join(current_lines[:i+1]))
                if i < len(current_lines) - 1:  # Not the last line
                    line_end_pos += 1  # Add newline character
                resume_position = line_end_pos
                
                # Add to skipped content
                if skipped_content:
                    skipped_content += '\n'
                skipped_content += current_line
                
            else:
                # Lines differ, stop checking
                break
        
        return duplicate_count, resume_position, skipped_content

    def calculate_smart_append_suffix(self, typed_text, current_text):
        """Calculate suffix with intelligent duplicate line detection and skipping."""
        if not current_text:
            return "", 0, ""
        
        if not typed_text:
            return current_text, 0, ""
        
        # First check if current text starts with typed text (basic prefix match)
        if not current_text.startswith(typed_text):
            # Text was reset or changed - return entire current text
            return current_text, 0, ""
        
        # Detect duplicate lines
        duplicate_count, resume_position, skipped_content = self.detect_duplicate_lines(typed_text, current_text)
        
        if duplicate_count > 0:
            # We found duplicate lines - calculate suffix from resume position
            suffix = current_text[resume_position:] if resume_position < len(current_text) else ""
            
            # Update statistics
            self.duplicate_lines_skipped += duplicate_count
            self.total_lines_processed += duplicate_count
            self.last_duplicate_detection_time = time.time()
            
            return suffix, resume_position, skipped_content
        else:
            # No duplicates found - use standard append logic
            suffix = current_text[len(typed_text):]
            return suffix, len(typed_text), ""

def test_basic_duplicate_detection():
    """Test basic duplicate line detection functionality."""
    print("=== Testing Basic Duplicate Detection ===\n")
    
    typer = MockAutotyper()
    
    # Test Case 1: Simple duplicate lines
    print("Test Case 1: Simple duplicate lines")
    typed_text = "Hello world\nThis is a test"
    current_text = "Hello world\nThis is a test\nNew content here"
    
    duplicate_count, resume_pos, skipped = typer.detect_duplicate_lines(typed_text, current_text)
    print(f"  Typed: {repr(typed_text)}")
    print(f"  Current: {repr(current_text)}")
    print(f"  Duplicates: {duplicate_count}, Resume: {resume_pos}, Skipped: {repr(skipped)}")
    
    assert duplicate_count == 2, f"Expected 2 duplicates, got {duplicate_count}"
    assert resume_pos == 27, f"Expected resume position 27, got {resume_pos}"  # "Hello world\nThis is a test\n"
    assert skipped == "Hello world\nThis is a test", f"Unexpected skipped content: {skipped}"
    print("  ✅ PASSED\n")
    
    # Test Case 2: Single word lines (should not be detected as duplicates)
    print("Test Case 2: Single word lines (should not be detected)")
    typed_text = "Hello\nWorld"
    current_text = "Hello\nWorld\nTest"
    
    duplicate_count, resume_pos, skipped = typer.detect_duplicate_lines(typed_text, current_text)
    print(f"  Typed: {repr(typed_text)}")
    print(f"  Current: {repr(current_text)}")
    print(f"  Duplicates: {duplicate_count}, Resume: {resume_pos}, Skipped: {repr(skipped)}")
    
    assert duplicate_count == 0, f"Expected 0 duplicates for single words, got {duplicate_count}"
    print("  ✅ PASSED\n")
    
    # Test Case 3: Partial duplicate (first line matches, second doesn't)
    print("Test Case 3: Partial duplicate detection")
    typed_text = "Hello world\nThis is different"
    current_text = "Hello world\nThis is a test\nNew content"
    
    duplicate_count, resume_pos, skipped = typer.detect_duplicate_lines(typed_text, current_text)
    print(f"  Typed: {repr(typed_text)}")
    print(f"  Current: {repr(current_text)}")
    print(f"  Duplicates: {duplicate_count}, Resume: {resume_pos}, Skipped: {repr(skipped)}")
    
    assert duplicate_count == 1, f"Expected 1 duplicate, got {duplicate_count}"
    assert resume_pos == 12, f"Expected resume position 12, got {resume_pos}"  # "Hello world\n"
    assert skipped == "Hello world", f"Unexpected skipped content: {skipped}"
    print("  ✅ PASSED\n")
    
    print("=== Basic Duplicate Detection Tests Passed! ===\n")

def test_smart_append_suffix():
    """Test the smart append suffix calculation with duplicate detection."""
    print("=== Testing Smart Append Suffix ===\n")
    
    typer = MockAutotyper()
    
    # Test Case 1: With duplicates
    print("Test Case 1: Smart suffix with duplicates")
    typed_text = "Hello world\nThis is a test"
    current_text = "Hello world\nThis is a test\nNew content here"
    
    suffix, resume_pos, skipped = typer.calculate_smart_append_suffix(typed_text, current_text)
    print(f"  Typed: {repr(typed_text)}")
    print(f"  Current: {repr(current_text)}")
    print(f"  Suffix: {repr(suffix)}")
    print(f"  Resume: {resume_pos}, Skipped: {repr(skipped)}")
    
    assert suffix == "New content here", f"Expected 'New content here', got {repr(suffix)}"
    assert resume_pos == 27, f"Expected resume position 27, got {resume_pos}"
    assert typer.duplicate_lines_skipped == 2, f"Expected 2 skipped lines, got {typer.duplicate_lines_skipped}"
    print("  ✅ PASSED\n")
    
    # Test Case 2: Without duplicates (normal append)
    print("Test Case 2: Smart suffix without duplicates")
    typer2 = MockAutotyper()
    typed_text = "Hello world"
    current_text = "Hello world and more text"
    
    suffix, resume_pos, skipped = typer2.calculate_smart_append_suffix(typed_text, current_text)
    print(f"  Typed: {repr(typed_text)}")
    print(f"  Current: {repr(current_text)}")
    print(f"  Suffix: {repr(suffix)}")
    print(f"  Resume: {resume_pos}, Skipped: {repr(skipped)}")
    
    assert suffix == " and more text", f"Expected ' and more text', got {repr(suffix)}"
    assert resume_pos == 11, f"Expected resume position 11, got {resume_pos}"
    assert skipped == "", f"Expected no skipped content, got {repr(skipped)}"
    assert typer2.duplicate_lines_skipped == 0, f"Expected 0 skipped lines, got {typer2.duplicate_lines_skipped}"
    print("  ✅ PASSED\n")
    
    print("=== Smart Append Suffix Tests Passed! ===\n")

def test_edge_cases():
    """Test edge cases for duplicate detection."""
    print("=== Testing Edge Cases ===\n")
    
    typer = MockAutotyper()
    
    # Test Case 1: Empty texts
    print("Test Case 1: Empty texts")
    duplicate_count, resume_pos, skipped = typer.detect_duplicate_lines("", "")
    assert duplicate_count == 0 and resume_pos == 0 and skipped == ""
    print("  ✅ PASSED\n")
    
    # Test Case 2: One empty text
    print("Test Case 2: One empty text")
    duplicate_count, resume_pos, skipped = typer.detect_duplicate_lines("", "Hello world")
    assert duplicate_count == 0 and resume_pos == 0 and skipped == ""
    print("  ✅ PASSED\n")
    
    # Test Case 3: Lines with only whitespace differences
    print("Test Case 3: Lines with whitespace differences")
    typed_text = "Hello world  \n  This is a test"
    current_text = "Hello world\nThis is a test\nNew content"
    
    duplicate_count, resume_pos, skipped = typer.detect_duplicate_lines(typed_text, current_text)
    print(f"  Duplicates: {duplicate_count}")
    assert duplicate_count == 2, f"Expected 2 duplicates (whitespace stripped), got {duplicate_count}"
    print("  ✅ PASSED\n")
    
    # Test Case 4: Mixed single and multi-word lines
    print("Test Case 4: Mixed single and multi-word lines")
    typed_text = "Hello\nHello world\nTest"
    current_text = "Hello\nHello world\nTest\nNew content here"
    
    duplicate_count, resume_pos, skipped = typer.detect_duplicate_lines(typed_text, current_text)
    print(f"  Duplicates: {duplicate_count}")
    # Should only detect "Hello world" as duplicate (multi-word), not "Hello" or "Test" (single words)
    assert duplicate_count == 0, f"Expected 0 duplicates (single words ignored), got {duplicate_count}"
    print("  ✅ PASSED\n")
    
    print("=== Edge Cases Tests Passed! ===\n")

def test_real_world_scenarios():
    """Test realistic scenarios that might occur during OCR capture."""
    print("=== Testing Real-World Scenarios ===\n")
    
    # Scenario 1: OCR captures overlapping content from previous typing
    print("Scenario 1: OCR overlap with previous content")
    typer = MockAutotyper()
    
    # User has typed some content
    typed_text = "The quick brown fox\njumps over the lazy dog\nThis is the end"
    
    # OCR captures overlapping content with new text
    current_text = "The quick brown fox\njumps over the lazy dog\nThis is the end\nAnd here is new content\nthat was just added"
    
    suffix, resume_pos, skipped = typer.calculate_smart_append_suffix(typed_text, current_text)
    
    print(f"  Previously typed: {len(typed_text)} chars")
    print(f"  OCR captured: {len(current_text)} chars")
    print(f"  Duplicates skipped: {typer.duplicate_lines_skipped} lines")
    print(f"  New content to type: {repr(suffix)}")
    
    expected_suffix = "And here is new content\nthat was just added"
    assert suffix == expected_suffix, f"Expected {repr(expected_suffix)}, got {repr(suffix)}"
    assert typer.duplicate_lines_skipped == 3, f"Expected 3 skipped lines, got {typer.duplicate_lines_skipped}"
    print("  ✅ PASSED\n")
    
    # Scenario 2: Document with repeated headers/footers
    print("Scenario 2: Document with repeated elements")
    typer2 = MockAutotyper()
    
    typed_text = "Chapter One Introduction\nThis is the first chapter\nPage 1"
    current_text = "Chapter One Introduction\nThis is the first chapter\nPage 1\nChapter Two Methods\nThis describes the methods\nPage 2"
    
    suffix, resume_pos, skipped = typer2.calculate_smart_append_suffix(typed_text, current_text)
    
    print(f"  Duplicates skipped: {typer2.duplicate_lines_skipped} lines")
    print(f"  New content: {repr(suffix)}")
    
    expected_suffix = "Chapter Two Methods\nThis describes the methods\nPage 2"
    assert suffix == expected_suffix, f"Expected {repr(expected_suffix)}, got {repr(suffix)}"
    print("  ✅ PASSED\n")
    
    print("=== Real-World Scenarios Tests Passed! ===\n")

if __name__ == "__main__":
    print("Testing intelligent duplicate line detection feature...\n")
    
    test_basic_duplicate_detection()
    test_smart_append_suffix()
    test_edge_cases()
    test_real_world_scenarios()
    
    print("🎉 ALL DUPLICATE DETECTION TESTS PASSED!")
    print("\nThe intelligent duplicate line detection feature is working correctly:")
    print("✅ Detects complete duplicate lines (multi-word only)")
    print("✅ Skips typing redundant content")
    print("✅ Resumes typing from the correct position")
    print("✅ Maintains append-only safety")
    print("✅ Handles edge cases and real-world scenarios")
    print("\nThe autotyper will now efficiently skip duplicate content while preserving safety!")
