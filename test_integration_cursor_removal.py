#!/usr/bin/env python3
"""
Integration test for cursor artifact removal with existing features.
This test verifies that cursor removal works seamlessly with duplicate detection
and trailing space functionality.
"""

import time
import re

class MockIntegratedAutotyper:
    """Mock autotyper with cursor removal, duplicate detection, and trailing space features."""
    
    def __init__(self):
        # Basic state
        self.typed_text = ""
        self.current_text = ""
        self.cursor_position = 0
        
        # Cursor removal state
        self.cursor_artifacts_removed = 0
        self.cursor_removal_enabled = True
        self.last_cursor_removal_time = 0
        
        # Duplicate detection state
        self.duplicate_lines_skipped = 0
        self.total_lines_processed = 0
        self.last_duplicate_detection_time = 0
        
        # Trailing space state
        self.text_stability_threshold = 1.0
        self.last_typing_completion_time = 0
        self.text_stable_since = 0
        self.trailing_space_added = False
        self.last_stable_text = ""
    
    def remove_cursor_artifacts(self, text):
        """Remove cursor artifacts from OCR text."""
        if not text or not self.cursor_removal_enabled:
            return text
        
        original_text = text
        
        # Apply cursor removal patterns
        text = self.remove_repeated_vertical_patterns(text)
        text = self.clean_all_word_internal_cursors(text)
        text = self.remove_standalone_vertical_bars(text)
        text = self.remove_cursor_misdetections(text)
        
        # Update statistics if any changes were made
        if text != original_text:
            self.cursor_artifacts_removed += 1
            self.last_cursor_removal_time = time.time()
            print(f"Cursor artifacts removed: '{original_text}' -> '{text}'")
        
        return text
    
    def remove_repeated_vertical_patterns(self, text):
        """Remove repeated vertical line patterns."""
        if not text:
            return text
        text = re.sub(r'\|{2,}', ' ', text)
        text = re.sub(r'(\|\s*){2,}', ' ', text)
        text = re.sub(r'[l1i\|]{4,}', ' ', text)
        return text
    
    def clean_all_word_internal_cursors(self, text):
        """Clean cursor artifacts within all words."""
        if not text:
            return text

        # Process line by line to preserve newlines
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            words = line.split()
            cleaned_words = [self.clean_word_internal_cursors(word) for word in words]
            cleaned_lines.append(' '.join(cleaned_words))

        return '\n'.join(cleaned_lines)
    
    def clean_word_internal_cursors(self, word):
        """Remove cursor artifacts within words."""
        if len(word) <= 1:
            return word
        
        # Check for structured content like filenames
        if '|' in word:
            parts = word.split('|')
            if len(parts) == 2 and len(parts[0]) >= 3 and len(parts[1]) >= 3:
                if self.looks_like_structured_content(parts[0], parts[1]):
                    return word  # Let standalone removal handle it
        
        # Remove internal cursors
        cleaned = word
        while '|' in cleaned and re.search(r'([a-zA-Z])\|([a-zA-Z])', cleaned):
            cleaned = re.sub(r'([a-zA-Z])\|([a-zA-Z])', r'\1\2', cleaned)
        
        # Clean leading/trailing cursors
        if len(word) > 1:
            cleaned = re.sub(r'^\|+', '', cleaned)
            cleaned = re.sub(r'\|+$', '', cleaned)
        
        return cleaned
    
    def looks_like_structured_content(self, part1, part2):
        """Check if parts look like structured content."""
        if '.' in part1 and len(part1.split('.')[-1]) <= 4:
            return True
        return False
    
    def remove_standalone_vertical_bars(self, text):
        """Remove standalone vertical bars."""
        if not text:
            return text
        # Preserve newlines by not treating them as generic whitespace
        text = re.sub(r'[ \t]+\|[ \t]+', ' ', text)
        text = re.sub(r'(\w)\|(\w)', r'\1 \2', text)
        text = re.sub(r'^\|[ \t]*', '', text, flags=re.MULTILINE)
        text = re.sub(r'[ \t]*\|$', '', text, flags=re.MULTILINE)
        text = re.sub(r'\|+', ' ', text)
        return text
    
    def remove_cursor_misdetections(self, text):
        """Remove cursor misdetections."""
        if not text:
            return text

        # Process line by line to preserve newlines
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            words = line.split()
            cleaned_words = []
            for i, word in enumerate(words):
                if not self.is_likely_cursor_misdetection(word, i, words):
                    cleaned_words.append(word)
            cleaned_lines.append(' '.join(cleaned_words))

        return '\n'.join(cleaned_lines)
    
    def is_likely_cursor_misdetection(self, word, position, all_words):
        """Check if word is likely a cursor misdetection."""
        if len(word) != 1:
            return False
        char = word.lower()
        if char in {'a', 'i'}:  # Legitimate single letters
            return False
        if char in {'|', 'l', '1'}:
            return True
        return False
    
    def detect_duplicate_lines(self, typed_text, current_text):
        """Detect duplicate lines."""
        if not typed_text or not current_text:
            return 0, 0, ""
        
        typed_lines = typed_text.split('\n')
        current_lines = current_text.split('\n')
        
        duplicate_count = 0
        resume_position = 0
        skipped_content = ""
        
        min_lines = min(len(typed_lines), len(current_lines))
        
        for i in range(min_lines):
            typed_line = typed_lines[i].strip()
            current_line = current_lines[i].strip()
            
            if (typed_line == current_line and 
                len(typed_line.split()) > 1 and
                len(typed_line) > 0):
                
                duplicate_count += 1
                resume_position = 0
                for j in range(i + 1):
                    resume_position += len(current_lines[j])
                    if j < i:
                        resume_position += 1
                
                if i < len(current_lines) - 1:
                    resume_position += 1
                
                if skipped_content:
                    skipped_content += '\n'
                skipped_content += current_line
            else:
                break
        
        return duplicate_count, resume_position, skipped_content
    
    def calculate_smart_append_suffix(self, typed_text, current_text):
        """Calculate suffix with duplicate detection."""
        if not current_text:
            return "", 0, ""
        
        if not typed_text:
            return current_text, 0, ""
        
        if not current_text.startswith(typed_text):
            return current_text, 0, ""
        
        duplicate_count, resume_position, skipped_content = self.detect_duplicate_lines(typed_text, current_text)
        
        if duplicate_count > 0:
            suffix = current_text[resume_position:] if resume_position < len(current_text) else ""
            self.duplicate_lines_skipped += duplicate_count
            self.total_lines_processed += duplicate_count
            self.last_duplicate_detection_time = time.time()
            return suffix, resume_position, skipped_content
        else:
            suffix = current_text[len(typed_text):]
            return suffix, len(typed_text), ""
    
    def check_and_add_trailing_space(self):
        """Check if conditions are met to add a trailing space."""
        current_time = time.time()
        
        if self.typed_text != self.current_text:
            self.trailing_space_added = False
            return False
            
        if self.trailing_space_added:
            return False
            
        if current_time - self.last_typing_completion_time < self.text_stability_threshold:
            return False
            
        if self.current_text != self.last_stable_text:
            self.last_stable_text = self.current_text
            self.text_stable_since = current_time
            return False
            
        if current_time - self.text_stable_since < self.text_stability_threshold:
            return False
            
        if not self.current_text or self.current_text[-1] in ' \t\n':
            return False
            
        # Add trailing space
        self.typed_text += ' '
        self.cursor_position += 1
        self.trailing_space_added = True
        return True

def test_cursor_removal_with_duplicate_detection():
    """Test cursor removal working with duplicate detection."""
    print("=== Testing Cursor Removal + Duplicate Detection ===\n")
    
    typer = MockIntegratedAutotyper()
    
    # Scenario: OCR captures text with cursor artifacts and duplicates
    print("Scenario 1: Cursor artifacts in duplicate content")
    
    # Previously typed content
    typer.typed_text = "Hello world\nThis is a test"
    typer.current_text = "Hello world\nThis is a test"
    
    # OCR captures overlapping content with cursor artifacts
    raw_ocr_text = "Hel|lo world\nThis | is a test\nNew con|tent here"
    
    # Step 1: Remove cursor artifacts
    cleaned_text = typer.remove_cursor_artifacts(raw_ocr_text)
    print(f"  Raw OCR: '{raw_ocr_text}'")
    print(f"  After cursor removal: '{cleaned_text}'")
    
    # Step 2: Apply duplicate detection
    typer.current_text = cleaned_text
    suffix, _, _ = typer.calculate_smart_append_suffix(typer.typed_text, typer.current_text)
    
    print(f"  Duplicates skipped: {typer.duplicate_lines_skipped}")
    print(f"  Suffix to type: '{suffix}'")
    
    assert typer.cursor_artifacts_removed == 1, f"Expected 1 cursor removal, got {typer.cursor_artifacts_removed}"
    assert typer.duplicate_lines_skipped == 2, f"Expected 2 duplicate lines, got {typer.duplicate_lines_skipped}"
    assert suffix == "New content here", f"Expected 'New content here', got '{suffix}'"
    
    print("  ✅ PASSED\n")

def test_cursor_removal_with_trailing_space():
    """Test cursor removal working with trailing space feature."""
    print("Scenario 2: Cursor removal followed by trailing space")
    
    typer = MockIntegratedAutotyper()
    
    # OCR captures text with cursor artifacts
    raw_ocr_text = "Hello wor|ld"
    
    # Step 1: Remove cursor artifacts
    cleaned_text = typer.remove_cursor_artifacts(raw_ocr_text)
    print(f"  Raw OCR: '{raw_ocr_text}'")
    print(f"  After cursor removal: '{cleaned_text}'")
    
    # Step 2: Simulate typing completion
    typer.typed_text = cleaned_text
    typer.current_text = cleaned_text
    typer.last_typing_completion_time = time.time() - 2.0
    typer.last_stable_text = cleaned_text
    typer.text_stable_since = time.time() - 2.0
    
    # Step 3: Check trailing space
    trailing_space_added = typer.check_and_add_trailing_space()
    
    print(f"  Cursor artifacts removed: {typer.cursor_artifacts_removed}")
    print(f"  Trailing space added: {trailing_space_added}")
    print(f"  Final text: '{typer.typed_text}'")
    
    assert typer.cursor_artifacts_removed == 1, "Expected cursor artifacts to be removed"
    assert trailing_space_added == True, "Expected trailing space to be added"
    assert typer.typed_text == "Hello world ", "Expected text with trailing space"
    
    print("  ✅ PASSED\n")

def test_complex_integration_scenario():
    """Test complex scenario with all features working together."""
    print("Scenario 3: Complex integration - all features together")
    
    typer = MockIntegratedAutotyper()
    
    # Previously typed content
    typer.typed_text = "Chapter One\nIntroduction text"
    typer.current_text = "Chapter One\nIntroduction text"
    
    # OCR captures new content with cursor artifacts and duplicates
    raw_ocr_text = "Chap|ter One\nIntro|duction text\nChap|ter Two\nNew con|tent"
    
    # Step 1: Remove cursor artifacts
    cleaned_text = typer.remove_cursor_artifacts(raw_ocr_text)
    print(f"  Raw OCR: '{raw_ocr_text}'")
    print(f"  After cursor removal: '{cleaned_text}'")
    
    # Step 2: Apply duplicate detection
    typer.current_text = cleaned_text
    suffix, _, _ = typer.calculate_smart_append_suffix(typer.typed_text, typer.current_text)
    
    # Step 3: Simulate typing the suffix
    typer.typed_text = typer.current_text
    typer.last_typing_completion_time = time.time() - 2.0
    typer.last_stable_text = typer.current_text
    typer.text_stable_since = time.time() - 2.0
    
    # Step 4: Check trailing space
    trailing_space_added = typer.check_and_add_trailing_space()
    
    print(f"  Cursor artifacts removed: {typer.cursor_artifacts_removed}")
    print(f"  Duplicates skipped: {typer.duplicate_lines_skipped}")
    print(f"  Suffix typed: '{suffix}'")
    print(f"  Trailing space added: {trailing_space_added}")
    print(f"  Final text: '{typer.typed_text}'")
    
    assert typer.cursor_artifacts_removed == 1, "Expected cursor artifacts to be removed"
    assert typer.duplicate_lines_skipped == 2, "Expected duplicate lines to be skipped"
    assert suffix == "Chapter Two\nNew content", f"Expected specific suffix, got '{suffix}'"
    assert trailing_space_added == True, "Expected trailing space to be added"
    
    print("  ✅ PASSED\n")

def test_structured_content_preservation():
    """Test that structured content like filenames is preserved correctly."""
    print("Scenario 4: Structured content preservation")
    
    typer = MockIntegratedAutotyper()
    
    # OCR captures filenames with cursor artifacts
    raw_ocr_text = "The files are data.txt|backup.txt and log.csv|report.csv"
    
    # Remove cursor artifacts
    cleaned_text = typer.remove_cursor_artifacts(raw_ocr_text)
    print(f"  Raw OCR: '{raw_ocr_text}'")
    print(f"  After cursor removal: '{cleaned_text}'")
    
    expected = "The files are data.txt backup.txt and log.csv report.csv"
    assert cleaned_text == expected, f"Expected '{expected}', got '{cleaned_text}'"
    
    print("  ✅ PASSED\n")

if __name__ == "__main__":
    print("Testing integration of cursor removal with existing features...\n")
    
    test_cursor_removal_with_duplicate_detection()
    test_cursor_removal_with_trailing_space()
    test_complex_integration_scenario()
    test_structured_content_preservation()
    
    print("🎉 ALL INTEGRATION TESTS PASSED!")
    print("\nThe intelligent cursor artifact removal feature integrates seamlessly with:")
    print("✅ Duplicate line detection and skipping")
    print("✅ Trailing space functionality")
    print("✅ Incremental typing system")
    print("✅ Structured content preservation")
    print("✅ Complex multi-step scenarios")
    print("\nThe enhanced autotyper provides comprehensive OCR text cleaning!")
