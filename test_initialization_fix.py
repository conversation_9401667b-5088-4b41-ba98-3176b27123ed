#!/usr/bin/env python3
"""
Test script to verify the initialization order fix for the trailing space feature.
This test verifies that the AttributeError has been resolved.
"""

def test_initialization_order():
    """Test that the initialization order issue has been fixed."""
    print("=== Testing Initialization Order Fix ===\n")
    
    try:
        # Import the main module
        import main
        print("✅ Main module imported successfully")
        
        # Create a minimal mock root to avoid GUI dependencies
        class MockRoot:
            def title(self, title): 
                pass
            def winfo_screenwidth(self): 
                return 1920
            def winfo_screenheight(self): 
                return 1080
            def quit(self): 
                pass
        
        # Mock the tkinter components to avoid GUI creation
        class MockWidget:
            def __init__(self, *args, **kwargs):
                self.value = "1.0"  # Default value for Entry widgets
            def pack(self, *args, **kwargs): 
                pass
            def insert(self, pos, text): 
                self.value = str(text)
            def get(self): 
                return self.value
            def delete(self, *args): 
                pass
        
        # Store original tkinter components
        original_components = {}
        for component_name in ['Button', 'Label', 'Entry', 'Text']:
            original_components[component_name] = getattr(main.tk, component_name)
            setattr(main.tk, component_name, MockWidget)
        
        try:
            # This should now work without AttributeError
            mock_root = MockRoot()
            app = main.ScreenTextTyper(mock_root)
            
            print("✅ ScreenTextTyper instantiated successfully (no AttributeError)")
            
            # Verify the attribute exists and has the correct default value
            assert hasattr(app, 'text_stability_threshold'), "text_stability_threshold attribute missing"
            assert app.text_stability_threshold == 1.0, f"Expected 1.0, got {app.text_stability_threshold}"
            print(f"✅ text_stability_threshold correctly initialized: {app.text_stability_threshold} seconds")
            
            # Verify GUI components exist
            assert hasattr(app, 'trailing_space_delay_entry'), "trailing_space_delay_entry missing"
            assert hasattr(app, 'trailing_space_delay_label'), "trailing_space_delay_label missing"
            print("✅ GUI components for trailing space delay created successfully")
            
            # Verify the GUI field has the correct default value
            default_value = app.trailing_space_delay_entry.get()
            assert default_value == "1.0", f"Expected '1.0', got '{default_value}'"
            print(f"✅ GUI field shows correct default value: '{default_value}'")
            
            # Test the validation function
            assert hasattr(app, 'validate_trailing_space_delay'), "validate_trailing_space_delay method missing"
            result = app.validate_trailing_space_delay("0.5")
            assert result == 0.5, f"Expected 0.5, got {result}"
            print("✅ Validation function works correctly")
            
            # Test edge cases for validation
            assert app.validate_trailing_space_delay("0.05") == 0.1  # Below minimum
            assert app.validate_trailing_space_delay("15.0") == 10.0  # Above maximum
            assert app.validate_trailing_space_delay("invalid") == 1.0  # Invalid input
            print("✅ Validation edge cases handled correctly")
            
        finally:
            # Restore original tkinter components
            for component_name, original_component in original_components.items():
                setattr(main.tk, component_name, original_component)
        
        print("\n=== Initialization Order Fix Test PASSED ===")
        return True
        
    except AttributeError as e:
        if "text_stability_threshold" in str(e):
            print(f"❌ AttributeError still exists: {e}")
            print("❌ The initialization order fix did not work")
            return False
        else:
            print(f"❌ Unexpected AttributeError: {e}")
            return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_functionality_verification():
    """Verify that all existing functionality remains intact."""
    print("\n=== Testing Functionality Verification ===\n")
    
    try:
        import main
        
        # Test that all expected methods exist
        expected_methods = [
            'start_typing', 'stop_typing', 'select_area', 'show_status',
            'check_and_add_trailing_space', 'validate_trailing_space_delay',
            'enhanced_typing_loop', 'process_text_changes'
        ]
        
        # Create a mock instance to check methods
        class MockRoot:
            def title(self, title): pass
            def winfo_screenwidth(self): return 1920
            def winfo_screenheight(self): return 1080
            def quit(self): pass
        
        class MockWidget:
            def __init__(self, *args, **kwargs): self.value = "1.0"
            def pack(self, *args, **kwargs): pass
            def insert(self, pos, text): self.value = str(text)
            def get(self): return self.value
            def delete(self, *args): pass
        
        # Mock tkinter
        original_components = {}
        for component_name in ['Button', 'Label', 'Entry', 'Text']:
            original_components[component_name] = getattr(main.tk, component_name)
            setattr(main.tk, component_name, MockWidget)
        
        try:
            app = main.ScreenTextTyper(MockRoot())
            
            for method_name in expected_methods:
                assert hasattr(app, method_name), f"Method {method_name} missing"
                print(f"✅ Method {method_name} exists")
            
            # Test that all expected attributes exist
            expected_attributes = [
                'text_stability_threshold', 'last_typing_completion_time',
                'text_stable_since', 'trailing_space_added', 'last_stable_text',
                'current_text', 'typed_text', 'typing_active'
            ]
            
            for attr_name in expected_attributes:
                assert hasattr(app, attr_name), f"Attribute {attr_name} missing"
                print(f"✅ Attribute {attr_name} exists")
            
        finally:
            # Restore tkinter
            for component_name, original_component in original_components.items():
                setattr(main.tk, component_name, original_component)
        
        print("\n=== Functionality Verification Test PASSED ===")
        return True
        
    except Exception as e:
        print(f"❌ Functionality verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing the initialization order fix for trailing space feature...\n")
    
    test1_passed = test_initialization_order()
    test2_passed = test_functionality_verification()
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The AttributeError has been fixed")
        print("✅ The application should now start without errors")
        print("✅ The trailing space delay field shows the correct default (1.0 seconds)")
        print("✅ All existing functionality remains intact")
        print("\nThe autotyper is ready to use with the configurable trailing space feature!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the implementation for remaining issues.")
