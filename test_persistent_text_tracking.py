#!/usr/bin/env python3
"""
Test script for the persistent text tracking feature.
This script tests the new cross-capture duplicate detection functionality.
"""

import time
import tempfile
import os

class MockPersistentAutotyper:
    """Mock autotyper class to test persistent text tracking logic."""
    
    def __init__(self):
        # State tracking
        self.typed_text = ""
        self.current_text = ""
        self.cursor_position = 0
        
        # Duplicate detection state
        self.duplicate_lines_skipped = 0
        self.total_lines_processed = 0
        self.last_duplicate_detection_time = 0
        
        # Cross-capture duplicate detection state
        self.cross_capture_duplicates_skipped = 0
        self.last_cross_capture_detection_time = 0
        
        # Persistent text tracking state
        self.temp_file_path = None
        self.temp_file_created = False
    
    def create_temp_file(self):
        """Create a temporary file for storing session text."""
        try:
            # Create temp file with timestamp
            timestamp = int(time.time())
            temp_dir = tempfile.gettempdir()
            self.temp_file_path = os.path.join(temp_dir, f"autotyper_session_{timestamp}.txt")
            
            # Create empty file
            with open(self.temp_file_path, 'w', encoding='utf-8') as f:
                f.write("")
            
            self.temp_file_created = True
            return True
            
        except Exception as e:
            print(f"Error creating temporary file: {e}")
            self.temp_file_path = None
            self.temp_file_created = False
            return False

    def save_capture_to_temp_file(self, text):
        """Save the current capture text to temporary file (replace contents)."""
        if not text:
            return False
            
        try:
            # Create temp file if it doesn't exist
            if not self.temp_file_created or not self.temp_file_path:
                if not self.create_temp_file():
                    return False
            
            # Replace entire file contents with new text
            with open(self.temp_file_path, 'w', encoding='utf-8') as f:
                f.write(text)
            
            return True
            
        except Exception as e:
            print(f"Error saving to temporary file: {e}")
            return False

    def load_previous_capture(self):
        """Load the previous capture text from temporary file."""
        if not self.temp_file_path or not os.path.exists(self.temp_file_path):
            return ""
            
        try:
            with open(self.temp_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return content
            
        except Exception as e:
            print(f"Error loading from temporary file: {e}")
            return ""

    def cleanup_temp_file(self):
        """Clean up the temporary file."""
        if self.temp_file_path and os.path.exists(self.temp_file_path):
            try:
                os.remove(self.temp_file_path)
            except Exception as e:
                print(f"Error cleaning up temporary file: {e}")
        
        self.temp_file_path = None
        self.temp_file_created = False

    def detect_cross_capture_duplicates(self, previous_text, current_text):
        """Detect overlapping content between previous capture and current capture."""
        if not previous_text or not current_text:
            return 0, 0, ""
        
        # Split both texts into lines
        previous_lines = previous_text.split('\n')
        current_lines = current_text.split('\n')
        
        # Look for overlap between the end of previous text and start of current text
        max_overlap_lines = min(len(previous_lines), len(current_lines), 10)
        
        best_overlap = 0
        best_resume_position = 0
        best_skipped_content = ""
        
        # Try different overlap sizes, starting from the largest
        for overlap_size in range(max_overlap_lines, 0, -1):
            # Get the last N lines from previous text
            prev_tail_lines = previous_lines[-overlap_size:]
            # Get the first N lines from current text
            curr_head_lines = current_lines[:overlap_size]
            
            # Check if they match
            matches = 0
            for i in range(overlap_size):
                prev_line = prev_tail_lines[i].strip()
                curr_line = curr_head_lines[i].strip()
                
                # Must be identical and contain multiple words
                if (prev_line == curr_line and 
                    len(prev_line.split()) > 1 and 
                    len(prev_line) > 0):
                    matches += 1
                else:
                    break  # Stop at first non-match
            
            # If we found a complete overlap, this is our best match
            if matches == overlap_size and matches > best_overlap:
                best_overlap = matches
                
                # Calculate resume position
                best_resume_position = 0
                for i in range(matches):
                    best_resume_position += len(current_lines[i])
                    if i < matches - 1:
                        best_resume_position += 1
                
                if matches < len(current_lines):
                    best_resume_position += 1
                
                # Build skipped content
                best_skipped_content = '\n'.join(curr_head_lines[:matches])
                
                break
        
        return best_overlap, best_resume_position, best_skipped_content

    def calculate_smart_append_suffix_with_file(self, current_text):
        """Enhanced suffix calculation that considers file-based previous capture."""
        if not current_text:
            return "", 0, ""
        
        # Try cross-capture duplicate detection with file contents
        previous_capture = self.load_previous_capture()
        
        if previous_capture:
            cross_capture_duplicates, cross_capture_resume_pos, cross_capture_skipped = \
                self.detect_cross_capture_duplicates(previous_capture, current_text)
            
            if cross_capture_duplicates > 0:
                # Found cross-capture duplicates
                suffix = current_text[cross_capture_resume_pos:] if cross_capture_resume_pos < len(current_text) else ""
                
                # Update statistics
                self.cross_capture_duplicates_skipped += cross_capture_duplicates
                self.total_lines_processed += cross_capture_duplicates
                self.last_cross_capture_detection_time = time.time()
                
                return suffix, cross_capture_resume_pos, cross_capture_skipped
        
        # If no cross-capture duplicates found, return entire text
        return current_text, 0, ""

def test_temp_file_management():
    """Test temporary file creation, saving, and loading."""
    print("=== Testing Temporary File Management ===\n")
    
    typer = MockPersistentAutotyper()
    
    # Test 1: Create temporary file
    print("Test Case 1: Create temporary file")
    success = typer.create_temp_file()
    assert success, "Failed to create temporary file"
    assert typer.temp_file_created, "temp_file_created flag not set"
    assert typer.temp_file_path is not None, "temp_file_path not set"
    assert os.path.exists(typer.temp_file_path), "Temporary file does not exist"
    print(f"  ✅ Created: {typer.temp_file_path}")
    
    # Test 2: Save text to file
    print("Test Case 2: Save text to temporary file")
    test_text = "Hello world\nThis is a test\nLine three"
    success = typer.save_capture_to_temp_file(test_text)
    assert success, "Failed to save text to temporary file"
    print(f"  ✅ Saved: {len(test_text)} characters")
    
    # Test 3: Load text from file
    print("Test Case 3: Load text from temporary file")
    loaded_text = typer.load_previous_capture()
    assert loaded_text == test_text, f"Loaded text doesn't match. Expected: {test_text}, Got: {loaded_text}"
    print(f"  ✅ Loaded: {len(loaded_text)} characters")
    
    # Test 4: Replace file contents
    print("Test Case 4: Replace file contents")
    new_text = "New content\nReplaced everything\nCompletely different"
    success = typer.save_capture_to_temp_file(new_text)
    assert success, "Failed to replace file contents"
    loaded_text = typer.load_previous_capture()
    assert loaded_text == new_text, "File contents not replaced correctly"
    print(f"  ✅ Replaced: {len(new_text)} characters")
    
    # Test 5: Cleanup
    print("Test Case 5: Cleanup temporary file")
    temp_path = typer.temp_file_path  # Store path before cleanup
    typer.cleanup_temp_file()
    assert not os.path.exists(temp_path), "Temporary file still exists after cleanup"
    assert not typer.temp_file_created, "temp_file_created flag not reset"
    assert typer.temp_file_path is None, "temp_file_path not reset"
    print("  ✅ Cleaned up successfully")
    
    print("=== Temporary File Management Tests Passed! ===\n")

def test_cross_capture_duplicate_detection():
    """Test cross-capture duplicate detection logic."""
    print("=== Testing Cross-Capture Duplicate Detection ===\n")
    
    typer = MockPersistentAutotyper()
    
    # Test 1: Simple overlap detection
    print("Test Case 1: Simple overlap at beginning")
    previous_text = "Line one\nLine two\nLine three\nLine four"
    current_text = "Line three\nLine four\nNew line five\nNew line six"
    
    duplicates, resume_pos, skipped = typer.detect_cross_capture_duplicates(previous_text, current_text)
    
    print(f"  Previous: {repr(previous_text)}")
    print(f"  Current: {repr(current_text)}")
    print(f"  Duplicates: {duplicates}, Resume: {resume_pos}, Skipped: {repr(skipped)}")
    
    assert duplicates == 2, f"Expected 2 duplicates, got {duplicates}"
    assert skipped == "Line three\nLine four", f"Expected specific skipped content, got {repr(skipped)}"
    expected_suffix = "New line five\nNew line six"
    actual_suffix = current_text[resume_pos:]
    assert actual_suffix == expected_suffix, f"Expected suffix {repr(expected_suffix)}, got {repr(actual_suffix)}"
    print("  ✅ PASSED")
    
    # Test 2: No overlap
    print("Test Case 2: No overlap between captures")
    previous_text = "Completely different\nContent here"
    current_text = "Totally new\nContent there"
    
    duplicates, resume_pos, skipped = typer.detect_cross_capture_duplicates(previous_text, current_text)
    
    print(f"  Duplicates: {duplicates}, Resume: {resume_pos}, Skipped: {repr(skipped)}")
    assert duplicates == 0, f"Expected 0 duplicates, got {duplicates}"
    assert skipped == "", f"Expected empty skipped content, got {repr(skipped)}"
    print("  ✅ PASSED")
    
    # Test 3: Partial overlap (should not match)
    print("Test Case 3: Partial overlap (single word lines - should not match)")
    previous_text = "Hello\nWorld\nTest"
    current_text = "World\nTest\nNew"
    
    duplicates, resume_pos, skipped = typer.detect_cross_capture_duplicates(previous_text, current_text)
    
    print(f"  Duplicates: {duplicates}, Resume: {resume_pos}, Skipped: {repr(skipped)}")
    assert duplicates == 0, f"Expected 0 duplicates (single words), got {duplicates}"
    print("  ✅ PASSED")
    
    # Test 4: Large overlap
    print("Test Case 4: Large overlap")
    previous_text = "Line one content\nLine two content\nLine three content\nLine four content\nLine five content"
    current_text = "Line three content\nLine four content\nLine five content\nNew line six\nNew line seven"
    
    duplicates, resume_pos, skipped = typer.detect_cross_capture_duplicates(previous_text, current_text)
    
    print(f"  Duplicates: {duplicates}, Resume: {resume_pos}, Skipped: {repr(skipped)}")
    assert duplicates == 3, f"Expected 3 duplicates, got {duplicates}"
    expected_suffix = "New line six\nNew line seven"
    actual_suffix = current_text[resume_pos:]
    assert actual_suffix == expected_suffix, f"Expected suffix {repr(expected_suffix)}, got {repr(actual_suffix)}"
    print("  ✅ PASSED")
    
    print("=== Cross-Capture Duplicate Detection Tests Passed! ===\n")

def test_integrated_workflow():
    """Test the complete integrated workflow."""
    print("=== Testing Integrated Workflow ===\n")
    
    typer = MockPersistentAutotyper()
    
    # Scenario: User captures overlapping content across multiple OCR captures
    print("Scenario: Multiple OCR captures with overlapping content")
    
    # First capture (no temp file exists yet)
    print("Step 1: First OCR capture")
    first_capture = "Chapter One Introduction\nThis is the first paragraph\nIt contains important information\nAbout the topic"

    # Process first capture (should return entire text since no temp file exists)
    suffix, _, _ = typer.calculate_smart_append_suffix_with_file(first_capture)
    print(f"  First capture suffix: {repr(suffix[:50])}...")
    assert suffix == first_capture, "First capture should return entire text as suffix"

    # Now save it to temp file for next capture
    typer.save_capture_to_temp_file(first_capture)
    print("  ✅ First capture processed")
    
    # Second capture with overlap
    print("Step 2: Second OCR capture with overlap")
    second_capture = "It contains important information\nAbout the topic\nChapter Two Methods\nThis describes the methodology"
    
    suffix, _, skipped = typer.calculate_smart_append_suffix_with_file(second_capture)
    print(f"  Cross-capture duplicates: {typer.cross_capture_duplicates_skipped}")
    print(f"  Skipped content: {repr(skipped)}")
    print(f"  Suffix to type: {repr(suffix)}")
    
    assert typer.cross_capture_duplicates_skipped == 2, f"Expected 2 cross-capture duplicates, got {typer.cross_capture_duplicates_skipped}"
    assert skipped == "It contains important information\nAbout the topic", "Unexpected skipped content"
    assert suffix == "Chapter Two Methods\nThis describes the methodology", "Unexpected suffix"
    print("  ✅ Second capture with overlap processed")
    
    # Save second capture to file
    typer.save_capture_to_temp_file(second_capture)
    
    # Third capture with different overlap
    print("Step 3: Third OCR capture with different overlap")
    third_capture = "This describes the methodology\nChapter Three Results\nThe results show that\nConclusions can be drawn"
    
    suffix, _, skipped = typer.calculate_smart_append_suffix_with_file(third_capture)
    print(f"  Cross-capture duplicates: {typer.cross_capture_duplicates_skipped}")
    print(f"  Skipped content: {repr(skipped)}")
    print(f"  Suffix to type: {repr(suffix)}")
    
    # Should find 1 more duplicate (total 3)
    assert typer.cross_capture_duplicates_skipped == 3, f"Expected 3 total cross-capture duplicates, got {typer.cross_capture_duplicates_skipped}"
    expected_suffix = "Chapter Three Results\nThe results show that\nConclusions can be drawn"
    assert suffix == expected_suffix, f"Expected suffix {repr(expected_suffix)}, got {repr(suffix)}"
    print("  ✅ Third capture with overlap processed")
    
    # Cleanup
    typer.cleanup_temp_file()
    print("  ✅ Cleanup completed")
    
    print("=== Integrated Workflow Tests Passed! ===\n")

def test_edge_cases():
    """Test edge cases and error conditions."""
    print("=== Testing Edge Cases ===\n")
    
    typer = MockPersistentAutotyper()
    
    # Test 1: Empty texts
    print("Test Case 1: Empty texts")
    duplicates, _, _ = typer.detect_cross_capture_duplicates("", "some text")
    assert duplicates == 0, "Should handle empty previous text"

    duplicates, _, _ = typer.detect_cross_capture_duplicates("some text", "")
    assert duplicates == 0, "Should handle empty current text"
    print("  ✅ PASSED")
    
    # Test 2: No temp file
    print("Test Case 2: No temporary file")
    suffix, _, _ = typer.calculate_smart_append_suffix_with_file("some text")
    assert suffix == "some text", "Should return entire text when no temp file"
    print("  ✅ PASSED")
    
    # Test 3: File I/O errors (simulate by using invalid path)
    print("Test Case 3: File I/O error handling")
    typer.temp_file_path = "/invalid/path/that/does/not/exist.txt"
    typer.temp_file_created = True
    
    # Should handle gracefully
    previous_text = typer.load_previous_capture()
    assert previous_text == "", "Should return empty string on file error"
    print("  ✅ PASSED")
    
    # Test 4: Whitespace differences
    print("Test Case 4: Whitespace differences in lines")
    previous_text = "Some previous content\nLine with multiple spaces   \nAnother line with tabs\t"
    current_text = "Line with multiple spaces\nAnother line with tabs\nNew content here"

    duplicates, _, _ = typer.detect_cross_capture_duplicates(previous_text, current_text)
    print(f"  Previous: {repr(previous_text)}")
    print(f"  Current: {repr(current_text)}")
    print(f"  Duplicates: {duplicates}")
    assert duplicates == 2, "Should handle whitespace differences correctly"
    print("  ✅ PASSED")
    
    print("=== Edge Cases Tests Passed! ===\n")

if __name__ == "__main__":
    print("Testing persistent text tracking feature...\n")
    
    try:
        test_temp_file_management()
        test_cross_capture_duplicate_detection()
        test_integrated_workflow()
        test_edge_cases()
        
        print("🎉 ALL PERSISTENT TEXT TRACKING TESTS PASSED!")
        print("\nThe persistent text tracking feature is working correctly:")
        print("✅ Creates and manages temporary session files")
        print("✅ Detects cross-capture duplicate content")
        print("✅ Calculates correct resume positions")
        print("✅ Handles edge cases and errors gracefully")
        print("✅ Integrates with existing duplicate detection")
        print("✅ Provides efficient cross-session duplicate skipping")
        print("\nThe autotyper will now efficiently skip content across captures!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
