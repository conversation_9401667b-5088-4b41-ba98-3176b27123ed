#!/usr/bin/env python3
"""
Test script with real-world OCR text wrapping examples.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_real_world_examples():
    """Test with realistic OCR text wrapping scenarios."""
    print("Testing real-world OCR text wrapping scenarios...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
    
    app = TestTyper()
    
    # Real-world test cases that might come from OCR
    real_world_cases = [
        {
            'name': 'PDF Text Wrap',
            'input': '''The quick brown fox jumps over the lazy
dog. This is a common typing test sent-
ence that demonstrates text wrapping
in documents.''',
            'expected': 'The quick brown fox jumps over the lazydog. This is a common typing test sent-ence that demonstrates text wrappingin documents.',
            'description': 'Typical PDF text with mid-word wrapping'
        },
        
        {
            'name': 'Email Text Wrap',
            'input': '''Hello <PERSON>,

I hope this email finds you well. I want-
ed to reach out regarding the upcoming
meeting scheduled for next week.

Best regards,
Jane''',
            'expected': 'Hello <PERSON>, I hope this email finds you well. I want-ed to reach out regarding the upcomingmeeting scheduled for next week. Best regards, <PERSON>',
            'description': '<PERSON><PERSON> with paragraph breaks and wrapping'
        },
        
        {
            'name': 'Technical Documentation',
            'input': '''The normalize_text() function handles
OCR inconsistencies by detecting wrap-
ped words and preserving actual spac-
es while removing artificial line breaks.''',
            'expected': 'The normalize_text() function handlesOCR inconsistencies by detecting wrap-ped words and preserving actual spac-es while removing artificial line breaks.',
            'description': 'Technical text with function names'
        },
        
        {
            'name': 'Book Text',
            'input': '''Chapter 1: Introduction

In the beginning, there was nothing but
darkness. Then came the light, illuminat-
ing the world with its brilliant radiance.

The story continues...''',
            'expected': 'Chapter 1: Introduction In the beginning, there was nothing butdarkness. Then came the light, illuminat-ing the world with its brilliant radiance. The story continues...',
            'description': 'Book text with chapters and paragraphs'
        },
        
        {
            'name': 'Code Comments',
            'input': '''// This function processes text normal-
// ization by handling line breaks intel-
// ligently to preserve word boundaries''',
            'expected': '// This function processes text normal-// ization by handling line breaks intel-// ligently to preserve word boundaries',
            'description': 'Code comments with consistent prefixes'
        },
        
        {
            'name': 'URL Wrapping',
            'input': '''Visit our website at https://www.exam-
ple.com/very/long/path/to/resource for
more information.''',
            'expected': 'Visit our website at https://www.exam-ple.com/very/long/path/to/resource formore information.',
            'description': 'URLs that get wrapped'
        },
        
        {
            'name': 'Numbers and Dates',
            'input': '''The meeting is scheduled for 2024-01-
15 at 3:30 PM in conference room 123-
A on the second floor.''',
            'expected': 'The meeting is scheduled for 2024-01-15 at 3:30 PM in conference room 123-A on the second floor.',
            'description': 'Dates and numbers with wrapping'
        },
        
        {
            'name': 'Mixed Punctuation',
            'input': '''Hello! How are you? I hope you're do-
ing well. Let's meet tomorrow at 2:00
PM, okay?''',
            'expected': 'Hello! How are you? I hope you\'re do-ing well. Let\'s meet tomorrow at 2:00PM, okay?',
            'description': 'Mixed punctuation with contractions'
        }
    ]
    
    print(f"\nTesting {len(real_world_cases)} real-world scenarios...\n")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(real_world_cases, 1):
        name = test_case['name']
        input_text = test_case['input']
        expected = test_case['expected']
        description = test_case['description']
        
        try:
            result = app.normalize_text(input_text)
            
            if result == expected:
                print(f"✅ {name}: {description}")
                print(f"   ✓ Correctly handled text wrapping")
                passed += 1
            else:
                print(f"❌ {name}: {description}")
                print(f"   Input preview: {repr(input_text[:50])}...")
                print(f"   Expected: {repr(expected[:100])}...")
                print(f"   Got:      {repr(result[:100])}...")
                failed += 1
                
        except Exception as e:
            print(f"💥 {name}: ERROR - {e}")
            failed += 1
        
        print()
    
    print(f"Real-world Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All real-world text normalization tests passed!")
        return True
    else:
        print(f"⚠️  {failed} real-world tests failed")
        return False

def demonstrate_before_after():
    """Demonstrate the improvement over the old normalization."""
    print("\nDemonstrating improvement over old normalization method...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
        
        def old_normalize_text(self, text):
            """Old normalization method for comparison."""
            if not text:
                return ""
            import re
            # Old method: just replace all whitespace with spaces
            normalized = re.sub(r'\s+', ' ', text.strip())
            normalized = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\]', '', normalized)
            return normalized
    
    app = TestTyper()
    
    test_examples = [
        "The quick brown fox\njumps over the lazy dog",
        "Hello \nworld",
        "hyphen-\nated",
        "First paragraph.\n\nSecond paragraph.",
        "Word wrap\nexample. New sentence\nstarts here."
    ]
    
    print("Comparison of old vs new normalization:\n")
    
    for example in test_examples:
        old_result = app.old_normalize_text(example)
        new_result = app.normalize_text(example)
        
        print(f"Input: {repr(example)}")
        print(f"Old:   {repr(old_result)}")
        print(f"New:   {repr(new_result)}")
        print(f"Improvement: {'✅' if old_result != new_result else '➖'}")
        print()

if __name__ == "__main__":
    try:
        success = test_real_world_examples()
        demonstrate_before_after()
        
        if success:
            print("\n🎉 All real-world tests passed! The enhanced text normalization is working correctly.")
            sys.exit(0)
        else:
            print("\n❌ Some real-world tests failed.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
