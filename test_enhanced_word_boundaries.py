#!/usr/bin/env python3
"""
Test script for enhanced word boundary detection with semantic analysis.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_word_boundaries():
    """Test the enhanced word boundary detection logic."""
    print("Testing enhanced word boundary detection with semantic analysis...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
    
    app = TestTyper()
    
    # Test cases with expected behavior based on semantic analysis
    test_cases = [
        # Cases that should be SEPARATED (add space)
        {
            'input': 'hello\nworld',
            'expected': 'hello world',
            'description': 'Two separate common words'
        },
        {
            'input': 'The\nquick',
            'expected': 'The quick',
            'description': 'Capitalized word followed by lowercase'
        },
        {
            'input': 'John\nSmith',
            'expected': 'John Smith',
            'description': 'Two capitalized proper nouns'
        },
        {
            'input': 'good\nmorning',
            'expected': 'good morning',
            'description': 'Two complete common words'
        },
        {
            'input': 'word\nother',
            'expected': 'word other',
            'description': 'Semantically unrelated words'
        },
        {
            'input': 'first\nSecond',
            'expected': 'first Second',
            'description': 'Lowercase followed by capitalized'
        },
        
        # Cases that should be JOINED (no space)
        {
            'input': 'some\nthing',
            'expected': 'something',
            'description': 'Common compound word'
        },
        {
            'input': 'every\nthing',
            'expected': 'everything',
            'description': 'Common compound word'
        },
        {
            'input': 'under\nstand',
            'expected': 'understand',
            'description': 'Common compound word'
        },
        {
            'input': 'hyphen-\nated',
            'expected': 'hyphen-ated',
            'description': 'Hyphenated word'
        },
        {
            'input': 'walk\ning',
            'expected': 'walking',
            'description': 'Word with suffix'
        },
        {
            'input': 'beauti\nful',
            'expected': 'beautiful',
            'description': 'Word with suffix'
        },
        {
            'input': 'develop\nment',
            'expected': 'development',
            'description': 'Word with suffix'
        },
        {
            'input': 'un\nhappy',
            'expected': 'unhappy',
            'description': 'Word with prefix'
        },
        {
            'input': 're\nwrite',
            'expected': 'rewrite',
            'description': 'Word with prefix'
        },
        
        # Edge cases
        {
            'input': 'hello \nworld',
            'expected': 'hello world',
            'description': 'Space before break (should preserve)'
        },
        {
            'input': 'hello\n world',
            'expected': 'hello world',
            'description': 'Space after break (should preserve)'
        },
        {
            'input': 'hello.\nWorld',
            'expected': 'hello. World',
            'description': 'Punctuation before break'
        },
        {
            'input': 'hello\n.world',
            'expected': 'hello .world',
            'description': 'Punctuation after break'
        },
        
        # Challenging cases
        {
            'input': 'any\nwhere',
            'expected': 'anywhere',
            'description': 'Compound word with "any"'
        },
        {
            'input': 'out\nside',
            'expected': 'outside',
            'description': 'Compound word with "out"'
        },
        {
            'input': 'up\ndate',
            'expected': 'update',
            'description': 'Compound word with "up"'
        },
        {
            'input': 'down\nload',
            'expected': 'download',
            'description': 'Compound word with "down"'
        },
    ]
    
    print(f"\nRunning {len(test_cases)} enhanced word boundary tests...\n")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        input_text = test_case['input']
        expected = test_case['expected']
        description = test_case['description']
        
        try:
            result = app.normalize_text(input_text)
            
            if result == expected:
                print(f"✅ Test {i}: {description}")
                print(f"   Input: {repr(input_text)} → Output: {repr(result)}")
                passed += 1
            else:
                print(f"❌ Test {i}: {description}")
                print(f"   Input: {repr(input_text)}")
                print(f"   Expected: {repr(expected)}")
                print(f"   Got: {repr(result)}")
                failed += 1
                
        except Exception as e:
            print(f"💥 Test {i}: {description} - ERROR: {e}")
            failed += 1
        
        print()
    
    print(f"Enhanced Word Boundary Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All enhanced word boundary tests passed!")
        return True
    else:
        print(f"⚠️  {failed} tests failed")
        return False

def test_semantic_analysis_components():
    """Test individual components of the semantic analysis."""
    print("\nTesting individual semantic analysis components...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
    
    app = TestTyper()
    
    # Test word continuation detection
    print("1. Testing word continuation detection:")
    continuation_tests = [
        ('walk', 'ing', True, 'Common suffix'),
        ('beauti', 'ful', True, 'Common suffix'),
        ('develop', 'ment', True, 'Common suffix'),
        ('hello', 'world', False, 'Separate words'),
        ('un', 'happy', True, 'Prefix pattern'),
        ('re', 'write', True, 'Prefix pattern'),
    ]
    
    for last_word, first_word, expected, desc in continuation_tests:
        result = app.is_likely_word_continuation(last_word, first_word)
        status = "✅" if result == expected else "❌"
        print(f"   {status} {desc}: '{last_word}' + '{first_word}' = {result}")
    
    # Test compound word detection
    print("\n2. Testing compound word detection:")
    compound_tests = [
        ('some', 'thing', True, 'Common compound'),
        ('every', 'where', True, 'Common compound'),
        ('under', 'stand', True, 'Common compound'),
        ('hello', 'world', False, 'Not a compound'),
        ('any', 'time', True, 'Common compound'),
        ('out', 'side', True, 'Common compound'),
    ]
    
    for last_word, first_word, expected, desc in compound_tests:
        result = app.is_likely_compound_word(last_word, first_word)
        status = "✅" if result == expected else "❌"
        print(f"   {status} {desc}: '{last_word}' + '{first_word}' = {result}")
    
    # Test separate word detection
    print("\n3. Testing separate word detection:")
    separate_tests = [
        ('hello', 'World', True, 'Capitalization suggests new word'),
        ('John', 'Smith', True, 'Two proper nouns'),
        ('good', 'morning', True, 'Complete common words'),
        ('some', 'thing', False, 'Compound word'),
        ('walk', 'ing', False, 'Word continuation'),
    ]
    
    for last_word, first_word, expected, desc in separate_tests:
        result = app.suggests_separate_words(last_word, first_word)
        status = "✅" if result == expected else "❌"
        print(f"   {status} {desc}: '{last_word}' + '{first_word}' = {result}")

def test_fallback_behavior():
    """Test that uncertain cases default to adding spaces."""
    print("\nTesting fallback behavior for uncertain cases...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
    
    app = TestTyper()
    
    # Test cases where the algorithm should be uncertain and default to spaces
    uncertain_cases = [
        'random\ntext',
        'unknown\nwords',
        'xyz\nabc',
        'test\ncase',
    ]
    
    for case in uncertain_cases:
        result = app.normalize_text(case)
        # Should default to adding space when uncertain
        expected_with_space = case.replace('\n', ' ')
        
        if ' ' in result:
            print(f"✅ Uncertain case defaulted to space: {repr(case)} → {repr(result)}")
        else:
            print(f"⚠️  Uncertain case joined words: {repr(case)} → {repr(result)}")

if __name__ == "__main__":
    try:
        success1 = test_enhanced_word_boundaries()
        test_semantic_analysis_components()
        test_fallback_behavior()
        
        if success1:
            print("\n🎉 Enhanced word boundary detection is working correctly!")
            print("The algorithm now properly distinguishes between:")
            print("  • Wrapped words that should be joined (e.g., 'some\\nthing' → 'something')")
            print("  • Separate words that should remain separated (e.g., 'hello\\nworld' → 'hello world')")
            sys.exit(0)
        else:
            print("\n❌ Some enhanced word boundary tests failed.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
