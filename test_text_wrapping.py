#!/usr/bin/env python3
"""
Test script for the enhanced text normalization with line break handling.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_text_normalization():
    """Test the enhanced text normalization functionality."""
    print("Testing enhanced text normalization with line break handling...")
    
    # Import without initializing GUI
    from main import ScreenTextTyper
    
    # Create instance with minimal initialization
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize only the necessary attributes for testing
            pass
    
    app = TestTyper()
    
    # Test cases for line break handling
    test_cases = [
        # Basic wrapped word cases
        {
            'input': 'hello\nworld',
            'expected': 'helloworld',
            'description': 'Simple word wrap (no spaces)'
        },
        {
            'input': 'hello \nworld',
            'expected': 'hello world',
            'description': 'Space before line break'
        },
        {
            'input': 'hello\n world',
            'expected': 'hello world',
            'description': 'Space after line break'
        },
        {
            'input': 'hello \n world',
            'expected': 'hello world',
            'description': 'Spaces before and after line break'
        },
        
        # Paragraph break cases
        {
            'input': 'hello\n\nworld',
            'expected': 'hello world',
            'description': 'Double line break (paragraph)'
        },
        {
            'input': 'hello\n\n\nworld',
            'expected': 'hello world',
            'description': 'Multiple line breaks'
        },
        
        # Punctuation cases
        {
            'input': 'hello.\nworld',
            'expected': 'hello. world',
            'description': 'Period before line break'
        },
        {
            'input': 'hello,\nworld',
            'expected': 'hello, world',
            'description': 'Comma before line break'
        },
        {
            'input': 'hello\n.world',
            'expected': 'hello .world',
            'description': 'Period after line break'
        },
        
        # Hyphenation cases
        {
            'input': 'hyphen-\nated',
            'expected': 'hyphen-ated',
            'description': 'Hyphenated word wrap'
        },
        {
            'input': 'non-\nbreaking',
            'expected': 'non-breaking',
            'description': 'Hyphenated compound word'
        },
        
        # Complex cases
        {
            'input': 'This is a long\nsentence that has\nbeen wrapped across\nmultiple lines.',
            'expected': 'This is a longsentence that hasbeen wrapped acrossmultiple lines.',
            'description': 'Multiple wrapped lines'
        },
        {
            'input': 'Word wrap\nexample. New sentence\nstarts here.',
            'expected': 'Word wrapexample. New sentencestarts here.',
            'description': 'Mixed wrapping and sentences'
        },
        {
            'input': 'First paragraph.\n\nSecond paragraph\nwith wrapping.',
            'expected': 'First paragraph. Second paragraphwith wrapping.',
            'description': 'Paragraph break with wrapping'
        },
        
        # Edge cases
        {
            'input': '',
            'expected': '',
            'description': 'Empty string'
        },
        {
            'input': 'single',
            'expected': 'single',
            'description': 'Single word'
        },
        {
            'input': '\n\n\n',
            'expected': '',
            'description': 'Only line breaks'
        },
        {
            'input': '   hello   \n   world   ',
            'expected': 'hello world',
            'description': 'Extra whitespace with line break'
        },
    ]
    
    print(f"\nRunning {len(test_cases)} test cases...\n")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        input_text = test_case['input']
        expected = test_case['expected']
        description = test_case['description']
        
        try:
            result = app.normalize_text(input_text)
            
            if result == expected:
                print(f"✅ Test {i}: {description}")
                print(f"   Input: {repr(input_text)}")
                print(f"   Result: {repr(result)}")
                passed += 1
            else:
                print(f"❌ Test {i}: {description}")
                print(f"   Input: {repr(input_text)}")
                print(f"   Expected: {repr(expected)}")
                print(f"   Got: {repr(result)}")
                failed += 1
                
        except Exception as e:
            print(f"💥 Test {i}: {description} - ERROR: {e}")
            failed += 1
        
        print()
    
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All text normalization tests passed!")
        return True
    else:
        print(f"⚠️  {failed} tests failed")
        return False

def test_word_wrapping_detection():
    """Test the word wrapping detection logic specifically."""
    print("\nTesting word wrapping detection logic...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
    
    app = TestTyper()
    
    # Test cases for is_word_wrapped function
    wrap_test_cases = [
        # Should be detected as wrapped
        ('hello', 'world', True, 'Alphanumeric continuation'),
        ('test', 'ing', True, 'Word continuation'),
        ('hyphen-', 'ated', True, 'Hyphenated word'),
        ('some', 'thing', True, 'Simple word wrap'),
        
        # Should NOT be detected as wrapped
        ('hello ', 'world', False, 'Space before break'),
        ('hello', ' world', False, 'Space after break'),
        ('hello.', 'world', False, 'Period before break'),
        ('hello', '.world', False, 'Period after break'),
        ('hello,', 'world', False, 'Comma before break'),
        ('hello!', 'world', False, 'Exclamation before break'),
        ('hello?', 'world', False, 'Question mark before break'),
        ('', 'world', False, 'Empty previous line'),
        ('hello', '', False, 'Empty current line'),
    ]
    
    print(f"Running {len(wrap_test_cases)} word wrap detection tests...\n")
    
    passed = 0
    failed = 0
    
    for i, (prev_line, current_line, expected, description) in enumerate(wrap_test_cases, 1):
        try:
            result = app.is_word_wrapped(prev_line, current_line)
            
            if result == expected:
                print(f"✅ Wrap Test {i}: {description}")
                print(f"   '{prev_line}' + '{current_line}' = {result}")
                passed += 1
            else:
                print(f"❌ Wrap Test {i}: {description}")
                print(f"   '{prev_line}' + '{current_line}' = {result} (expected {expected})")
                failed += 1
                
        except Exception as e:
            print(f"💥 Wrap Test {i}: {description} - ERROR: {e}")
            failed += 1
    
    print(f"\nWrap Detection Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All word wrap detection tests passed!")
        return True
    else:
        print(f"⚠️  {failed} wrap detection tests failed")
        return False

if __name__ == "__main__":
    try:
        success1 = test_text_normalization()
        success2 = test_word_wrapping_detection()
        
        if success1 and success2:
            print("\n🎉 All tests passed! Text normalization with line break handling is working correctly.")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
