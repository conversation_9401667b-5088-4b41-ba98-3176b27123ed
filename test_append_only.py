#!/usr/bin/env python3
"""
Test script for the append-only autotyper functionality.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_append_only_logic():
    """Test the append-only text processing functions."""
    print("Testing append-only autotyper functionality...")
    
    # Import without initializing GUI
    from main import ScreenTextTyper
    
    # Create instance with minimal initialization
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize only the necessary attributes for testing
            self.delay = 0.1
            self.error_rate = 0.05
            self.variance = 0.1
            self.current_text = ""
            self.previous_text = ""
            self.typed_text = ""
            self.cursor_position = 0
            from collections import deque
            self.text_history = deque(maxlen=10)
            self.typing_active = False
            self.capture_active = False
    
    app = TestTyper()
    
    # Test 1: Simple append scenario
    print("\n1. Testing simple append scenario...")
    app.typed_text = "Hello"
    app.current_text = "Hello world"
    
    suffix = app.calculate_append_suffix(app.typed_text, app.current_text)
    print(f"   Typed: '{app.typed_text}'")
    print(f"   Current: '{app.current_text}'")
    print(f"   Suffix to append: '{suffix}'")
    assert suffix == " world", f"Expected ' world', got: '{suffix}'"
    print("   ✓ Simple append works correctly")
    
    # Test 2: No new text scenario
    print("\n2. Testing no new text scenario...")
    app.typed_text = "Hello world"
    app.current_text = "Hello world"
    
    suffix = app.calculate_append_suffix(app.typed_text, app.current_text)
    print(f"   Typed: '{app.typed_text}'")
    print(f"   Current: '{app.current_text}'")
    print(f"   Suffix to append: '{suffix}'")
    assert suffix == "", f"Expected empty string, got: '{suffix}'"
    print("   ✓ No new text scenario works correctly")
    
    # Test 3: Text reset scenario
    print("\n3. Testing text reset scenario...")
    app.typed_text = "Hello world"
    app.current_text = "Goodbye world"
    
    suffix = app.calculate_append_suffix(app.typed_text, app.current_text)
    print(f"   Typed: '{app.typed_text}'")
    print(f"   Current: '{app.current_text}'")
    print(f"   Suffix to append: '{suffix}'")
    assert suffix == "Goodbye world", f"Expected 'Goodbye world', got: '{suffix}'"
    print("   ✓ Text reset scenario works correctly")
    
    # Test 4: Empty typed text scenario
    print("\n4. Testing empty typed text scenario...")
    app.typed_text = ""
    app.current_text = "New text"
    
    suffix = app.calculate_append_suffix(app.typed_text, app.current_text)
    print(f"   Typed: '{app.typed_text}'")
    print(f"   Current: '{app.current_text}'")
    print(f"   Suffix to append: '{suffix}'")
    assert suffix == "New text", f"Expected 'New text', got: '{suffix}'"
    print("   ✓ Empty typed text scenario works correctly")
    
    # Test 5: Prefix matching
    print("\n5. Testing prefix matching logic...")
    test_cases = [
        ("Hello", "Hello world!", " world!"),
        ("", "Hello", "Hello"),
        ("Hello world", "Hello world", ""),
        ("Hello", "Hi there", "Hi there"),  # Reset case
        ("Test", "Testing 123", "ing 123"),
    ]
    
    for typed, current, expected in test_cases:
        result = app.calculate_append_suffix(typed, current)
        print(f"   '{typed}' -> '{current}' = '{result}'")
        assert result == expected, f"Expected '{expected}', got '{result}'"
    
    print("   ✓ All prefix matching tests passed")
    
    # Test 6: Text normalization still works
    print("\n6. Testing text normalization...")
    test_text = "Hello   world!  \n\n  This is a test."
    normalized = app.normalize_text(test_text)
    print(f"   Original: {repr(test_text)}")
    print(f"   Normalized: {repr(normalized)}")
    assert normalized == "Hello world! This is a test.", f"Expected normalized text, got: {normalized}"
    print("   ✓ Text normalization works correctly")
    
    print("\n✅ All append-only tests passed! The autotyper is ready for safe, append-only operation.")
    return True

def test_safety_features():
    """Test that deletion and replacement operations are removed."""
    print("\nTesting safety features...")

    from main import ScreenTextTyper

    # Create a minimal test instance
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Skip GUI initialization
            pass

    # Check that deletion/replacement methods are removed or disabled
    app = TestTyper()
    
    # These methods should not exist or should be safe
    dangerous_methods = ['handle_text_deletion', 'handle_text_replacement']
    
    for method_name in dangerous_methods:
        if hasattr(app, method_name):
            print(f"   ⚠️  Warning: {method_name} still exists")
        else:
            print(f"   ✓ {method_name} safely removed")
    
    # Check that we have the new append-only methods
    safe_methods = ['calculate_append_suffix', 'position_cursor_at_end']
    
    for method_name in safe_methods:
        if hasattr(app, method_name):
            print(f"   ✓ {method_name} available")
        else:
            print(f"   ❌ {method_name} missing")
    
    print("   ✅ Safety features verified")

if __name__ == "__main__":
    try:
        test_append_only_logic()
        test_safety_features()
        print("\n🎉 All tests passed! Append-only autotyper is working correctly.")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
