#!/usr/bin/env python3
"""
Debug newline preservation in cursor removal.
"""

import re

def remove_repeated_vertical_patterns(text):
    """Remove repeated vertical line patterns."""
    print(f"remove_repeated_vertical_patterns input: {repr(text)}")
    if not text:
        return text
    text = re.sub(r'\|{2,}', ' ', text)
    text = re.sub(r'(\|\s*){2,}', ' ', text)
    text = re.sub(r'[l1i\|]{4,}', ' ', text)
    print(f"remove_repeated_vertical_patterns output: {repr(text)}")
    return text

def clean_word_internal_cursors(word):
    """Remove cursor artifacts within words."""
    print(f"  clean_word_internal_cursors input: {repr(word)}")
    if len(word) <= 1:
        return word
    
    # Remove internal cursors
    cleaned = word
    while '|' in cleaned and re.search(r'([a-zA-Z])\|([a-zA-Z])', cleaned):
        cleaned = re.sub(r'([a-zA-Z])\|([a-zA-Z])', r'\1\2', cleaned)
    
    # Clean leading/trailing cursors
    if len(word) > 1:
        cleaned = re.sub(r'^\|+', '', cleaned)
        cleaned = re.sub(r'\|+$', '', cleaned)
    
    print(f"  clean_word_internal_cursors output: {repr(cleaned)}")
    return cleaned

def clean_all_word_internal_cursors(text):
    """Clean cursor artifacts within all words."""
    print(f"clean_all_word_internal_cursors input: {repr(text)}")
    if not text:
        return text

    # Process line by line to preserve newlines
    lines = text.split('\n')
    print(f"  split into lines: {lines}")
    cleaned_lines = []

    for line in lines:
        words = line.split()
        print(f"    line words: {words}")
        cleaned_words = [clean_word_internal_cursors(word) for word in words]
        cleaned_lines.append(' '.join(cleaned_words))

    result = '\n'.join(cleaned_lines)
    print(f"clean_all_word_internal_cursors output: {repr(result)}")
    return result

def remove_standalone_vertical_bars(text):
    """Remove standalone vertical bars."""
    print(f"remove_standalone_vertical_bars input: {repr(text)}")
    if not text:
        return text
    
    # Preserve newlines by not treating them as generic whitespace
    text = re.sub(r'[ \t]+\|[ \t]+', ' ', text)
    print(f"  after pattern 1: {repr(text)}")
    
    text = re.sub(r'(\w)\|(\w)', r'\1 \2', text)
    print(f"  after pattern 2: {repr(text)}")
    
    text = re.sub(r'^\|[ \t]*', '', text, flags=re.MULTILINE)
    print(f"  after pattern 3a: {repr(text)}")
    
    text = re.sub(r'[ \t]*\|$', '', text, flags=re.MULTILINE)
    print(f"  after pattern 3b: {repr(text)}")
    
    text = re.sub(r'\|+', ' ', text)
    print(f"  after pattern 4: {repr(text)}")
    
    print(f"remove_standalone_vertical_bars output: {repr(text)}")
    return text

def remove_cursor_artifacts(text):
    """Remove cursor artifacts from OCR text."""
    print(f"=== Starting cursor removal ===")
    print(f"Input: {repr(text)}")
    
    # Apply cursor removal patterns in the right order
    text = remove_repeated_vertical_patterns(text)
    text = clean_all_word_internal_cursors(text)
    text = remove_standalone_vertical_bars(text)
    
    print(f"Final output: {repr(text)}")
    return text

def test_debug():
    """Debug the newline issue."""
    test_input = "Hel|lo world\nThis | is a test\nNew con|tent here"
    print(f"Testing: {repr(test_input)}")
    
    result = remove_cursor_artifacts(test_input)
    print(f"Result: {repr(result)}")
    
    expected = "Hello world\nThis is a test\nNew content here"
    print(f"Expected: {repr(expected)}")
    print(f"Match: {result == expected}")

if __name__ == "__main__":
    test_debug()
